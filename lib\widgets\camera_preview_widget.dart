import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import '../providers/camera_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/app_state_provider.dart';
import '../widgets/metadata_overlay.dart';
import '../widgets/gridlines_overlay.dart';

class CameraPreviewWidget extends StatefulWidget {
  const CameraPreviewWidget({super.key});

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  double _baseZoomLevel = 1.0;

  @override
  Widget build(BuildContext context) {
    return Consumer3<CameraProvider, SettingsProvider, AppStateProvider>(
      builder: (context, cameraProvider, settingsProvider, appStateProvider, child) {
        if (cameraProvider.controller == null || !cameraProvider.isInitialized) {
          return const Center(
            child: CircularProgressIndicator(color: Colors.white),
          );
        }

        final controller = cameraProvider.controller!;

        return GestureDetector(
          onScaleStart: (details) {
            // Store initial zoom level for pinch-to-zoom
            _baseZoomLevel = cameraProvider.zoomLevel;
          },
          onScaleUpdate: (details) {
            // Handle pinch-to-zoom with improved scaling
            final newZoom = (_baseZoomLevel * details.scale)
                .clamp(cameraProvider.minZoom, cameraProvider.maxZoom);
            cameraProvider.setZoomLevel(newZoom);
          },
          child: Stack(
            children: [
              // Camera preview - Full screen
              Positioned.fill(
                child: CameraPreview(controller),
              ),



              // Metadata overlay
              const Positioned.fill(
                child: MetadataOverlay(),
              ),
              

            ],
          ),
        );
      },
    );
  }


}
