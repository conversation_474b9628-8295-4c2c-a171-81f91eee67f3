import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;

import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:gal/gal.dart';
import 'package:saver_gallery/saver_gallery.dart';
import '../providers/app_state_provider.dart';
import '../providers/settings_provider.dart';
import 'permission_service.dart';
import 'orientation_service.dart';

enum MetadataPosition {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
}

class ImageService {
  Future<void> processAndSaveImage(
    XFile picture,
    AppStateProvider appStateProvider,
    SettingsProvider settingsProvider,
  ) async {
    try {
      // Check storage permission
      final storagePermission = await PermissionService.requestStoragePermission();
      if (!storagePermission) {
        throw Exception('Storage permission denied');
      }

      // Check if Gal has permission
      final hasAccess = await Gal.hasAccess();
      if (!hasAccess) {
        final requestResult = await Gal.requestAccess();
        if (!requestResult) {
          throw Exception('Gallery access permission denied');
        }
      }
      // Generate filename first for faster response
      final filename = _generateFilename(appStateProvider.projectName);
      print('DEBUG: Generated filename: $filename');

      // Process image with metadata overlay (single save operation)
      final bytes = await picture.readAsBytes();
      print('DEBUG: Starting single image processing with metadata...');
      final originalImage = img.decodeImage(bytes);

      if (originalImage == null) {
        throw Exception('Failed to decode image');
      }

      print('DEBUG: Processing image with metadata...');

      // Create a copy to work with
      final processedImage = img.Image.from(originalImage);

      // Apply multiple metadata storage methods
      print('DEBUG: Starting comprehensive metadata storage...');

      // Method 1: Simple and Guaranteed Visual Overlay
      try {
        _addSimpleGuaranteedOverlay(processedImage, appStateProvider);
        print('DEBUG: Simple overlay completed successfully');
      } catch (e) {
        print('ERROR: Simple overlay failed: $e');
        // Add emergency fallback
        _addEmergencyOverlay(processedImage);
      }

      // Method 2: Sidecar JSON file for complete metadata preservation
      await _createMetadataSidecarFile(appStateProvider, filename);

      // Verify metadata overlay was applied by checking image
      final overlayVerified = _verifyOverlayApplied(processedImage);
      if (!overlayVerified) {
        print('WARNING: Overlay verification failed, adding emergency overlay');
        _addEmergencyOverlay(processedImage);
      }

      // Encode processed image
      final encodedImage = img.encodeJpg(processedImage, quality: 100);

      // Save processed image to temp for gallery upload
      final tempDir = await getTemporaryDirectory();
      final processedPath = '${tempDir.path}/processed_$filename';
      final processedFile = File(processedPath);
      await processedFile.writeAsBytes(encodedImage);

      print('DEBUG: Processed image saved to temp: $processedPath');

      // Save to gallery using single method to avoid duplicates
      bool saved = false;
      String? errorMessage;

      print('DEBUG: Attempting to save processed image to gallery...');

      // Primary method: Try Gal package with processed image
      try {
        await Gal.putImage(processedPath, album: 'PCam');
        saved = true;
        print('SUCCESS: Image saved to gallery via Gal package');
      } catch (e) {
        print('WARNING: Gal package failed: $e');
        errorMessage = 'Gal failed: $e';

        // Fallback method: Try SaverGallery only if Gal completely failed
        try {
          final result = await SaverGallery.saveImage(
            encodedImage,
            fileName: filename,
            quality: 100,
            skipIfExists: false,
          );

          if (result.isSuccess) {
            saved = true;
            print('SUCCESS: Image saved to gallery via SaverGallery');
          } else {
            errorMessage = 'Both methods failed - Gal: $errorMessage; SaverGallery: ${result.errorMessage}';
            print('ERROR: SaverGallery also failed: ${result.errorMessage}');
          }
        } catch (e) {
          errorMessage = 'Both methods failed - Gal: $errorMessage; SaverGallery: $e';
          print('ERROR: SaverGallery exception: $e');
        }
      }

      // Clean up temporary files
      try {
        await processedFile.delete();
        print('DEBUG: Temporary processed file cleaned up');
      } catch (e) {
        print('WARNING: Failed to clean up temp file: $e');
      }

      if (!saved) {
        print('ERROR: All save methods failed');
        throw Exception('Failed to save image to gallery: $errorMessage');
      }

      print('SUCCESS: Image processing and saving completed - only one image saved');

    } catch (e) {
      throw Exception('Failed to process image: $e');
    }
  }

  Future<void> _addMetadataOverlay(
    img.Image image,
    AppStateProvider appStateProvider,
    SettingsProvider settingsProvider,
  ) async {
    print('DEBUG: _addMetadataOverlay called');
    print('DEBUG: Image dimensions: ${image.width}x${image.height}');

    final metadata = _buildMetadataText(appStateProvider);
    print('DEBUG: Built metadata text: "$metadata"');
    print('DEBUG: Metadata length: ${metadata.length}');

    if (metadata.isEmpty) {
      print('WARNING: Metadata is empty, adding fallback overlay');
      _addSimpleFallbackOverlay(image, appStateProvider);
      return;
    }

    // Calculate text properties - optimized for speed and visibility
    final fontSize = (settingsProvider.fontSize * 3).round(); // Balanced scale for speed and visibility
    final color = _colorToImageColor(settingsProvider.fontColor);
    
    // Use sensor-based orientation detection for more accuracy
    final orientationService = OrientationService();
    final sensorIsLandscape = orientationService.isLandscape;

    // Fallback to image dimensions if sensor is not available
    final imageIsLandscape = image.width > image.height;
    final isLandscape = sensorIsLandscape; // Prefer sensor data

    final orientationPosition = isLandscape
        ? MetadataPosition.topLeft
        : MetadataPosition.bottomLeft;

    print('DEBUG: Image dimensions: ${image.width}x${image.height}');
    print('DEBUG: Image-based landscape: $imageIsLandscape');
    print('DEBUG: Sensor-based landscape: $sensorIsLandscape');
    print('DEBUG: Final landscape decision: $isLandscape');
    print('DEBUG: Metadata position: $orientationPosition');

    // Always draw a large visible rectangle first as fallback
    _drawFallbackMetadataIndicator(image, isLandscape);

    // Also add a simple test marker to verify overlay is working
    _addSimpleTestMarker(image, isLandscape);

    if (isLandscape) {
      // For landscape mode: draw metadata rotated 90 degrees left (vertical)
      print('DEBUG: Drawing landscape metadata (90° rotated)');
      _drawLandscapeMetadata(image, metadata, fontSize, color);
    } else {
      // For portrait mode: draw metadata normally (horizontal)
      print('DEBUG: Drawing portrait metadata (horizontal)');
      final position = _calculateTextPosition(
        image,
        orientationPosition,
        isLandscape,
        metadata,
        fontSize,
      );
      _drawTextOnImage(image, metadata, position, fontSize, color);
      _drawMetadataConfirmationIndicator(image, position, metadata.split('\n').length);
    }

    print('DEBUG: Text drawing completed');
  }

  String _buildMetadataText(AppStateProvider appStateProvider) {
    print('DEBUG: Building metadata text...');
    final List<String> metadataLines = [];

    print('DEBUG: Project name: "${appStateProvider.projectName}"');
    if (appStateProvider.projectName.isNotEmpty) {
      metadataLines.add('Project: ${appStateProvider.projectName}');
    }

    print('DEBUG: Beneficiary name: "${appStateProvider.beneficiaryName}"');
    if (appStateProvider.beneficiaryName.isNotEmpty) {
      metadataLines.add('Beneficiary: ${appStateProvider.beneficiaryName}');
    }

    print('DEBUG: G.P.: "${appStateProvider.gp}"');
    if (appStateProvider.gp.isNotEmpty) {
      metadataLines.add('G.P.: ${appStateProvider.gp}');
    }

    print('DEBUG: Location: "${appStateProvider.location}"');
    if (appStateProvider.location.isNotEmpty) {
      metadataLines.add('Location: ${appStateProvider.location}');
    }

    print('DEBUG: Note: "${appStateProvider.note}"');
    if (appStateProvider.note.isNotEmpty) {
      metadataLines.add('Note: ${appStateProvider.note}');
    }

    if (appStateProvider.latitude != null && appStateProvider.longitude != null) {
      metadataLines.add(
        'GPS: ${appStateProvider.latitude!.toStringAsFixed(6)}, ${appStateProvider.longitude!.toStringAsFixed(6)}',
      );
    }

    if (appStateProvider.captureDateTime != null) {
      final formatter = DateFormat('dd/MM/yyyy HH:mm:ss');
      metadataLines.add('Date: ${formatter.format(appStateProvider.captureDateTime!)}');
    } else {
      // Always add current date/time as fallback
      final formatter = DateFormat('dd/MM/yyyy HH:mm:ss');
      metadataLines.add('Date: ${formatter.format(DateTime.now())}');
    }

    // Always add at least one line for testing
    if (metadataLines.isEmpty) {
      print('WARNING: No metadata lines found, adding fallback');
      metadataLines.add('PCam Photo - ${DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now())}');
    }

    final result = metadataLines.join('\n');
    print('DEBUG: Final metadata text: "$result"');
    print('DEBUG: Metadata lines count: ${metadataLines.length}');
    return result;
  }

  img.ColorRgb8 _colorToImageColor(Color color) {
    return img.ColorRgb8((color.r * 255.0).round() & 0xff, (color.g * 255.0).round() & 0xff, (color.b * 255.0).round() & 0xff);
  }

  Point<int> _calculateTextPosition(
    img.Image image,
    MetadataPosition position,
    bool isLandscape,
    String metadata,
    int fontSize,
  ) {
    // Increased padding for better visibility
    final padding = isLandscape ? 40 : 30;
    final lineHeight = fontSize + 10;
    final lines = metadata.split('\n').length;
    final totalTextHeight = lines * lineHeight;

    print('DEBUG: Calculating position for $position');
    print('DEBUG: Lines: $lines, Total height: $totalTextHeight');

    switch (position) {
      case MetadataPosition.topLeft:
        final point = Point(padding, padding + fontSize);
        print('DEBUG: Top-left position: ${point.x}, ${point.y}');
        return point;
      case MetadataPosition.topRight:
        final point = Point(image.width - 400 - padding, padding + fontSize);
        print('DEBUG: Top-right position: ${point.x}, ${point.y}');
        return point;
      case MetadataPosition.bottomLeft:
        final point = Point(padding, image.height - totalTextHeight - padding);
        print('DEBUG: Bottom-left position: ${point.x}, ${point.y}');
        return point;
      case MetadataPosition.bottomRight:
        final point = Point(
          image.width - 400 - padding,
          image.height - totalTextHeight - padding,
        );
        print('DEBUG: Bottom-right position: ${point.x}, ${point.y}');
        return point;
    }
  }

  void _drawTextOnImage(
    img.Image image,
    String text,
    Point<int> position,
    int fontSize,
    img.ColorRgb8 color,
  ) {
    final lines = text.split('\n');
    final lineHeight = fontSize + 5;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final y = position.y + (i * lineHeight);
      
      // Draw text with simple character rendering
      // Note: This is a simplified text rendering. For production,
      // consider using a more sophisticated text rendering library
      _drawSimpleText(image, line, position.x, y, fontSize, color);
    }
  }

  void _drawSimpleText(
    img.Image image,
    String text,
    int x,
    int y,
    int fontSize,
    img.ColorRgb8 color,
  ) {
    // Ultra-simple and reliable text rendering - just draw a large visible rectangle
    final lines = text.split('\n');
    final padding = 30;

    // Calculate dimensions for a simple large rectangle
    final maxLineLength = lines.map((line) => line.length).reduce((a, b) => a > b ? a : b);
    final totalWidth = maxLineLength * 12 + padding * 2; // Fixed width per character
    final totalHeight = lines.length * 25 + padding * 2; // Fixed height per line

    print('DEBUG: Drawing simple metadata rectangle - Width: $totalWidth, Height: $totalHeight');
    print('DEBUG: Position: $x, $y');
    print('DEBUG: Lines to render: ${lines.length}');

    // Draw large black background rectangle
    final bgColor = img.ColorRgb8(0, 0, 0);
    img.fillRect(
      image,
      x1: x,
      y1: y,
      x2: x + totalWidth,
      y2: y + totalHeight,
      color: bgColor,
    );

    // Draw thick white border
    final borderColor = img.ColorRgb8(255, 255, 255);
    final borderThickness = 8;

    // Draw border
    img.fillRect(image, x1: x, y1: y, x2: x + totalWidth, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + totalHeight - borderThickness, x2: x + totalWidth, y2: y + totalHeight, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + totalHeight, color: borderColor);
    img.fillRect(image, x1: x + totalWidth - borderThickness, y1: y, x2: x + totalWidth, y2: y + totalHeight, color: borderColor);

    // Draw simple text representation using the built-in drawString if available
    // If not available, draw simple patterns
    try {
      // Try to use built-in text drawing
      for (int lineIndex = 0; lineIndex < lines.length; lineIndex++) {
        final line = lines[lineIndex];
        final lineY = y + padding + (lineIndex * 25);

        // Draw each character as a simple white rectangle
        for (int charIndex = 0; charIndex < line.length; charIndex++) {
          final char = line[charIndex];
          if (char == ' ') continue;

          final charX = x + padding + (charIndex * 12);

          // Draw simple character block
          img.fillRect(
            image,
            x1: charX,
            y1: lineY,
            x2: charX + 10,
            y2: lineY + 18,
            color: borderColor,
          );

          // Add simple character differentiation
          _drawSimpleCharacterPattern(image, char, charX, lineY, borderColor, bgColor);
        }
      }
    } catch (e) {
      print('DEBUG: Text drawing error: $e');
      // Fallback: just draw a large white rectangle to show metadata was processed
      img.fillRect(
        image,
        x1: x + padding,
        y1: y + padding,
        x2: x + totalWidth - padding,
        y2: y + totalHeight - padding,
        color: borderColor,
      );
    }

    print('DEBUG: Simple metadata rendering completed');
  }

  void _drawSimpleCharacterPattern(
    img.Image image,
    String char,
    int x,
    int y,
    img.ColorRgb8 textColor,
    img.ColorRgb8 bgColor,
  ) {
    // Very simple character patterns for basic recognition
    switch (char.toUpperCase()) {
      case 'A':
        // Horizontal line
        img.fillRect(image, x1: x + 2, y1: y + 8, x2: x + 8, y2: y + 10, color: bgColor);
        break;
      case 'E':
        // Three horizontal lines
        img.fillRect(image, x1: x + 2, y1: y + 3, x2: x + 7, y2: y + 4, color: bgColor);
        img.fillRect(image, x1: x + 2, y1: y + 8, x2: x + 6, y2: y + 9, color: bgColor);
        img.fillRect(image, x1: x + 2, y1: y + 14, x2: x + 7, y2: y + 15, color: bgColor);
        break;
      case 'I':
        // Vertical line
        img.fillRect(image, x1: x + 4, y1: y + 2, x2: x + 6, y2: y + 16, color: bgColor);
        break;
      case 'O':
        // Inner rectangle
        img.fillRect(image, x1: x + 3, y1: y + 4, x2: x + 7, y2: y + 14, color: bgColor);
        break;
      case ':':
        // Two dots
        img.fillRect(image, x1: x + 4, y1: y + 5, x2: x + 6, y2: y + 7, color: bgColor);
        img.fillRect(image, x1: x + 4, y1: y + 11, x2: x + 6, y2: y + 13, color: bgColor);
        break;
    }
  }

  void _drawCharacterPattern(
    img.Image image,
    String char,
    int x,
    int y,
    int width,
    int height,
    img.ColorRgb8 bgColor,
  ) {
    // Add simple patterns to make characters more recognizable
    final centerX = x + width ~/ 2;
    final centerY = y + height ~/ 2;

    switch (char.toUpperCase()) {
      case 'A':
        // Draw horizontal line for A
        img.fillRect(
          image,
          x1: x + 2,
          y1: centerY,
          x2: x + width - 2,
          y2: centerY + 1,
          color: bgColor,
        );
        break;
      case 'E':
        // Draw horizontal lines for E
        img.fillRect(
          image,
          x1: x + 2,
          y1: y + 2,
          x2: x + width - 2,
          y2: y + 3,
          color: bgColor,
        );
        img.fillRect(
          image,
          x1: x + 2,
          y1: centerY,
          x2: x + width - 4,
          y2: centerY + 1,
          color: bgColor,
        );
        img.fillRect(
          image,
          x1: x + 2,
          y1: y + height - 3,
          x2: x + width - 2,
          y2: y + height - 2,
          color: bgColor,
        );
        break;
      case 'I':
        // Draw vertical line for I
        img.fillRect(
          image,
          x1: centerX,
          y1: y + 2,
          x2: centerX + 1,
          y2: y + height - 2,
          color: bgColor,
        );
        break;
      case 'O':
        // Draw inner rectangle for O
        img.fillRect(
          image,
          x1: x + 3,
          y1: y + 3,
          x2: x + width - 3,
          y2: y + height - 3,
          color: bgColor,
        );
        break;
      case ':':
        // Draw two dots for colon
        img.fillRect(
          image,
          x1: centerX,
          y1: y + 2,
          x2: centerX + 2,
          y2: y + 4,
          color: bgColor,
        );
        img.fillRect(
          image,
          x1: centerX,
          y1: y + height - 4,
          x2: centerX + 2,
          y2: y + height - 2,
          color: bgColor,
        );
        break;
    }
  }



  String _generateFilename(String projectName) {
    final now = DateTime.now();
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
    final projectPrefix = projectName.isNotEmpty
        ? '${projectName.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_')}_'
        : '';

    return '${projectPrefix}PCam_$timestamp.jpg';
  }



  void _drawMetadataConfirmationIndicator(img.Image image, Point<int> position, int lineCount) {
    // Draw a small colored indicator to confirm metadata processing
    final indicatorColor = img.ColorRgb8(0, 255, 0); // Bright green
    final indicatorSize = 20;

    // Draw small green square in corner as confirmation
    img.fillRect(
      image,
      x1: position.x - 35,
      y1: position.y - 35,
      x2: position.x - 15,
      y2: position.y - 15,
      color: indicatorColor,
    );

    // Add white border around indicator
    final borderColor = img.ColorRgb8(255, 255, 255);

    // Top border
    img.fillRect(
      image,
      x1: position.x - 37,
      y1: position.y - 37,
      x2: position.x - 13,
      y2: position.y - 35,
      color: borderColor,
    );

    // Bottom border
    img.fillRect(
      image,
      x1: position.x - 37,
      y1: position.y - 15,
      x2: position.x - 13,
      y2: position.y - 13,
      color: borderColor,
    );

    // Left border
    img.fillRect(
      image,
      x1: position.x - 37,
      y1: position.y - 37,
      x2: position.x - 35,
      y2: position.y - 13,
      color: borderColor,
    );

    // Right border
    img.fillRect(
      image,
      x1: position.x - 15,
      y1: position.y - 37,
      x2: position.x - 13,
      y2: position.y - 13,
      color: borderColor,
    );

    print('DEBUG: Metadata confirmation indicator drawn at position: ${position.x - 35}, ${position.y - 35}');
  }

  void _drawLandscapeMetadata(
    img.Image image,
    String metadata,
    int fontSize,
    img.ColorRgb8 color,
  ) {
    // Simplified landscape metadata - just draw a large rectangle for now
    final lines = metadata.split('\n');
    final padding = 30;

    // Simple dimensions
    final totalWidth = lines.length * 25 + padding * 2;
    final totalHeight = 200 + padding * 2; // Fixed height

    // Position at top-left for landscape
    final startX = padding;
    final startY = padding;

    print('DEBUG: Simple landscape metadata - Width: $totalWidth, Height: $totalHeight');
    print('DEBUG: Starting position: $startX, $startY');

    // Draw large black background rectangle
    final bgColor = img.ColorRgb8(0, 0, 0);
    img.fillRect(
      image,
      x1: startX,
      y1: startY,
      x2: startX + totalWidth,
      y2: startY + totalHeight,
      color: bgColor,
    );

    // Draw thick white border
    final borderColor = img.ColorRgb8(255, 255, 255);
    final borderThickness = 8;

    // Draw border
    img.fillRect(image, x1: startX, y1: startY, x2: startX + totalWidth, y2: startY + borderThickness, color: borderColor);
    img.fillRect(image, x1: startX, y1: startY + totalHeight - borderThickness, x2: startX + totalWidth, y2: startY + totalHeight, color: borderColor);
    img.fillRect(image, x1: startX, y1: startY, x2: startX + borderThickness, y2: startY + totalHeight, color: borderColor);
    img.fillRect(image, x1: startX + totalWidth - borderThickness, y1: startY, x2: startX + totalWidth, y2: startY + totalHeight, color: borderColor);

    // Draw simple vertical columns for each line
    for (int lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      final line = lines[lineIndex];
      final columnX = startX + padding + (lineIndex * 25);

      print('DEBUG: Drawing vertical column $lineIndex for: "$line"');

      // Draw a vertical white rectangle for each line
      img.fillRect(
        image,
        x1: columnX,
        y1: startY + padding,
        x2: columnX + 20,
        y2: startY + totalHeight - padding,
        color: borderColor,
      );

      // Add some pattern to differentiate columns
      if (lineIndex % 2 == 0) {
        // Add black stripes for even columns
        for (int i = 0; i < 5; i++) {
          final stripeY = startY + padding + 20 + (i * 30);
          img.fillRect(
            image,
            x1: columnX + 2,
            y1: stripeY,
            x2: columnX + 18,
            y2: stripeY + 10,
            color: bgColor,
          );
        }
      }
    }

    // Draw confirmation indicator for landscape
    _drawLandscapeConfirmationIndicator(image, startX, startY);

    print('DEBUG: Simple landscape metadata rendering completed');
  }

  void _drawVerticalCharacterPattern(
    img.Image image,
    String char,
    int x,
    int y,
    int width,
    int height,
    img.ColorRgb8 bgColor,
  ) {
    // Add simple patterns for vertical characters
    final centerX = x + width ~/ 2;
    final centerY = y + height ~/ 2;

    switch (char.toUpperCase()) {
      case 'A':
        // Horizontal line for A
        img.fillRect(image, x1: x + 2, y1: centerY, x2: x + width - 2, y2: centerY + 1, color: bgColor);
        break;
      case 'E':
        // Multiple horizontal lines for E
        img.fillRect(image, x1: x + 2, y1: y + 2, x2: x + width - 2, y2: y + 3, color: bgColor);
        img.fillRect(image, x1: x + 2, y1: centerY, x2: x + width - 4, y2: centerY + 1, color: bgColor);
        img.fillRect(image, x1: x + 2, y1: y + height - 3, x2: x + width - 2, y2: y + height - 2, color: bgColor);
        break;
      case 'I':
        // Vertical line for I
        img.fillRect(image, x1: centerX, y1: y + 2, x2: centerX + 1, y2: y + height - 2, color: bgColor);
        break;
      case 'O':
        // Inner rectangle for O
        img.fillRect(image, x1: x + 3, y1: y + 3, x2: x + width - 3, y2: y + height - 3, color: bgColor);
        break;
      case ':':
        // Two dots for colon
        img.fillRect(image, x1: centerX, y1: y + 2, x2: centerX + 2, y2: y + 4, color: bgColor);
        img.fillRect(image, x1: centerX, y1: y + height - 4, x2: centerX + 2, y2: y + height - 2, color: bgColor);
        break;
    }
  }

  void _drawLandscapeConfirmationIndicator(img.Image image, int x, int y) {
    // Draw green confirmation indicator for landscape mode
    final indicatorColor = img.ColorRgb8(0, 255, 0);
    final borderColor = img.ColorRgb8(255, 255, 255);

    // Draw green square
    img.fillRect(image, x1: x - 35, y1: y - 35, x2: x - 15, y2: y - 15, color: indicatorColor);

    // Draw white border
    img.fillRect(image, x1: x - 37, y1: y - 37, x2: x - 13, y2: y - 35, color: borderColor);
    img.fillRect(image, x1: x - 37, y1: y - 15, x2: x - 13, y2: y - 13, color: borderColor);
    img.fillRect(image, x1: x - 37, y1: y - 37, x2: x - 35, y2: y - 13, color: borderColor);
    img.fillRect(image, x1: x - 15, y1: y - 37, x2: x - 13, y2: y - 13, color: borderColor);

    print('DEBUG: Landscape confirmation indicator drawn at: ${x - 35}, ${y - 35}');
  }

  void _drawFallbackMetadataIndicator(img.Image image, bool isLandscape) {
    // Draw a large, highly visible rectangle to ensure metadata presence is always visible
    final indicatorColor = img.ColorRgb8(255, 0, 255); // Bright magenta
    final borderColor = img.ColorRgb8(255, 255, 255); // White border

    int x, y, width, height;

    if (isLandscape) {
      // Top-left for landscape
      x = 20;
      y = 20;
      width = 150;
      height = 300;
    } else {
      // Bottom-left for portrait
      x = 20;
      y = image.height - 320;
      width = 300;
      height = 150;
    }

    print('DEBUG: Drawing fallback metadata indicator at $x, $y (${width}x$height)');

    // Draw bright magenta rectangle
    img.fillRect(
      image,
      x1: x,
      y1: y,
      x2: x + width,
      y2: y + height,
      color: indicatorColor,
    );

    // Draw thick white border
    final borderThickness = 10;
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + height - borderThickness, x2: x + width, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x + width - borderThickness, y1: y, x2: x + width, y2: y + height, color: borderColor);

    // Add "METADATA" text pattern using simple rectangles
    final textY = y + height ~/ 2 - 20;
    final letterWidth = 15;
    final letterSpacing = 20;

    // Draw simple letter patterns for "META"
    for (int i = 0; i < 4; i++) {
      final letterX = x + 20 + (i * letterSpacing);

      // Draw letter rectangle
      img.fillRect(
        image,
        x1: letterX,
        y1: textY,
        x2: letterX + letterWidth,
        y2: textY + 40,
        color: borderColor,
      );

      // Add letter-specific patterns
      switch (i) {
        case 0: // M
          img.fillRect(image, x1: letterX + 5, y1: textY + 10, x2: letterX + 10, y2: textY + 15, color: indicatorColor);
          break;
        case 1: // E
          img.fillRect(image, x1: letterX + 3, y1: textY + 5, x2: letterX + 12, y2: textY + 8, color: indicatorColor);
          img.fillRect(image, x1: letterX + 3, y1: textY + 18, x2: letterX + 10, y2: textY + 21, color: indicatorColor);
          img.fillRect(image, x1: letterX + 3, y1: textY + 32, x2: letterX + 12, y2: textY + 35, color: indicatorColor);
          break;
        case 2: // T
          img.fillRect(image, x1: letterX + 3, y1: textY + 5, x2: letterX + 12, y2: textY + 8, color: indicatorColor);
          break;
        case 3: // A
          img.fillRect(image, x1: letterX + 3, y1: textY + 18, x2: letterX + 12, y2: textY + 21, color: indicatorColor);
          break;
      }
    }

    print('DEBUG: Fallback metadata indicator drawn successfully');
  }

  void _addSimpleFallbackOverlay(img.Image image, AppStateProvider appStateProvider) {
    print('DEBUG: Adding simple fallback overlay...');

    // Draw a very simple but highly visible overlay
    final red = img.ColorRgb8(255, 0, 0);
    final white = img.ColorRgb8(255, 255, 255);
    final black = img.ColorRgb8(0, 0, 0);

    // Determine position based on image orientation
    final isLandscape = image.width > image.height;
    int x, y, width, height;

    if (isLandscape) {
      // Top-left for landscape
      x = 20;
      y = 20;
      width = 200;
      height = 400;
    } else {
      // Bottom-left for portrait
      x = 20;
      y = image.height - 420;
      width = 400;
      height = 200;
    }

    print('DEBUG: Fallback overlay position: $x, $y (${width}x$height)');

    // Draw red background
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + height, color: red);

    // Draw white border
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + 15, color: white);
    img.fillRect(image, x1: x, y1: y + height - 15, x2: x + width, y2: y + height, color: white);
    img.fillRect(image, x1: x, y1: y, x2: x + 15, y2: y + height, color: white);
    img.fillRect(image, x1: x + width - 15, y1: y, x2: x + width, y2: y + height, color: white);

    // Add simple text indicators
    final textY = y + 30;
    final lineHeight = 25;

    // Draw simple rectangles to represent text lines
    final lines = [
      'PROJECT: ${appStateProvider.projectName}',
      'BENEFICIARY: ${appStateProvider.beneficiaryName}',
      'GP: ${appStateProvider.gp}',
      'LOCATION: ${appStateProvider.location}',
      'NOTE: ${appStateProvider.note}',
    ];

    for (int i = 0; i < lines.length && i < 6; i++) {
      final lineY = textY + (i * lineHeight);

      // Draw white rectangle for each line
      img.fillRect(
        image,
        x1: x + 20,
        y1: lineY,
        x2: x + width - 20,
        y2: lineY + 18,
        color: white,
      );

      // Add black stripes to differentiate lines
      if (i % 2 == 0) {
        img.fillRect(
          image,
          x1: x + 25,
          y1: lineY + 2,
          x2: x + width - 25,
          y2: lineY + 16,
          color: black,
        );
      }
    }

    print('DEBUG: Simple fallback overlay completed');
  }

  void _verifyMetadataOverlay(img.Image image) {
    print('DEBUG: Verifying metadata overlay application...');

    // Check for colored pixels in expected metadata areas
    final isLandscape = image.width > image.height;

    // Define areas to check based on orientation
    int checkX, checkY, checkWidth, checkHeight;

    if (isLandscape) {
      // Top-left area for landscape
      checkX = 20;
      checkY = 20;
      checkWidth = 200;
      checkHeight = 200;
    } else {
      // Bottom-left area for portrait
      checkX = 20;
      checkY = image.height - 220;
      checkWidth = 200;
      checkHeight = 200;
    }

    print('DEBUG: Checking metadata area: $checkX, $checkY (${checkWidth}x$checkHeight)');

    // Sample pixels in the metadata area
    int coloredPixels = 0;
    int totalSamples = 0;

    for (int x = checkX; x < checkX + checkWidth; x += 10) {
      for (int y = checkY; y < checkY + checkHeight; y += 10) {
        if (x < image.width && y < image.height) {
          final pixel = image.getPixel(x, y);
          totalSamples++;

          // Check if pixel is not the default background color
          final r = pixel.r;
          final g = pixel.g;
          final b = pixel.b;

          // Look for non-natural colors (metadata indicators)
          if ((r > 200 && g < 100 && b < 100) || // Red
              (r > 200 && g < 100 && b > 200) || // Magenta
              (r < 100 && g < 100 && b < 100) || // Black
              (r > 200 && g > 200 && b > 200)) { // White
            coloredPixels++;
          }
        }
      }
    }

    final overlayPercentage = (coloredPixels / totalSamples) * 100;
    print('DEBUG: Metadata overlay verification:');
    print('DEBUG: - Total samples: $totalSamples');
    print('DEBUG: - Colored pixels: $coloredPixels');
    print('DEBUG: - Overlay coverage: ${overlayPercentage.toStringAsFixed(1)}%');

    if (overlayPercentage > 10) {
      print('SUCCESS: Metadata overlay detected in image!');
    } else {
      print('WARNING: Metadata overlay may not be visible in image!');
      // Force add a simple test overlay
      _addTestOverlay(image);
    }
  }

  void _addTestOverlay(img.Image image) {
    print('DEBUG: Adding test overlay for verification...');

    // Add a simple but very visible test overlay
    final testColor = img.ColorRgb8(0, 255, 0); // Bright green
    final borderColor = img.ColorRgb8(255, 255, 255); // White

    final isLandscape = image.width > image.height;
    int x, y;

    if (isLandscape) {
      x = 50;
      y = 50;
    } else {
      x = 50;
      y = image.height - 150;
    }

    // Draw a bright green test rectangle
    img.fillRect(
      image,
      x1: x,
      y1: y,
      x2: x + 100,
      y2: y + 100,
      color: testColor,
    );

    // Add white border
    img.fillRect(image, x1: x, y1: y, x2: x + 100, y2: y + 5, color: borderColor);
    img.fillRect(image, x1: x, y1: y + 95, x2: x + 100, y2: y + 100, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + 5, y2: y + 100, color: borderColor);
    img.fillRect(image, x1: x + 95, y1: y, x2: x + 100, y2: y + 100, color: borderColor);

    print('DEBUG: Test overlay added at $x, $y');
  }

  void _addSimpleTestMarker(img.Image image, bool isLandscape) {
    // Add a small but very visible marker to prove overlay is working
    final markerColor = img.ColorRgb8(255, 255, 0); // Bright yellow

    int x, y;
    if (isLandscape) {
      x = image.width - 60;
      y = 20;
    } else {
      x = image.width - 60;
      y = image.height - 60;
    }

    // Draw small yellow square
    img.fillRect(
      image,
      x1: x,
      y1: y,
      x2: x + 40,
      y2: y + 40,
      color: markerColor,
    );

    print('DEBUG: Test marker added at $x, $y (yellow square)');
  }

  // Method 1: Advanced Text Rendering with Multiple Techniques
  Future<void> _addReliableVisualOverlay(
    img.Image image,
    AppStateProvider appStateProvider,
  ) async {
    print('DEBUG: Adding advanced visual overlay...');

    final metadata = _buildMetadataText(appStateProvider);
    final isLandscape = image.width > image.height;

    // Technique 1: Bitmap text rendering
    _drawBitmapText(image, metadata, isLandscape);

    // Technique 2: QR Code with metadata
    await _addMetadataQRCode(image, metadata, isLandscape);

    // Technique 3: Color-coded data blocks
    _addColorCodedMetadata(image, appStateProvider, isLandscape);

    // Technique 4: Steganographic metadata embedding
    _embedMetadataInPixels(image, metadata);

    // Technique 5: Visibility markers
    _addVisibilityMarkers(image, isLandscape);

    print('DEBUG: Advanced visual overlay completed');
  }

  // Simple and Guaranteed Overlay Method
  void _addSimpleGuaranteedOverlay(img.Image image, AppStateProvider appStateProvider) {
    print('DEBUG: Adding simple guaranteed overlay...');

    final isLandscape = image.width > image.height;

    // Define colors
    final bgColor = img.ColorRgb8(255, 0, 0);      // Bright red background
    final borderColor = img.ColorRgb8(255, 255, 0); // Yellow border
    final textColor = img.ColorRgb8(255, 255, 255);  // White text blocks

    // Position and size
    int x, y, width, height;
    if (isLandscape) {
      x = 50;
      y = 50;
      width = 600;
      height = 300;
    } else {
      x = 50;
      y = image.height - 350;
      width = image.width - 100;
      height = 300;
    }

    print('DEBUG: Drawing overlay at $x, $y (${width}x$height)');

    // Draw bright red background (impossible to miss)
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + height, color: bgColor);

    // Draw thick yellow border
    final borderThickness = 15;
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + height - borderThickness, x2: x + width, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x + width - borderThickness, y1: y, x2: x + width, y2: y + height, color: borderColor);

    // Draw metadata as simple white rectangles
    final metadata = [
      'PROJECT: ${appStateProvider.projectName}',
      'BENEFICIARY: ${appStateProvider.beneficiaryName}',
      'GP: ${appStateProvider.gp}',
      'LOCATION: ${appStateProvider.location}',
      'NOTE: ${appStateProvider.note}',
      'GPS: ${appStateProvider.latitude ?? 0}, ${appStateProvider.longitude ?? 0}',
      'DATE: ${DateTime.now().toString().substring(0, 19)}',
    ];

    for (int i = 0; i < metadata.length; i++) {
      final lineY = y + 30 + (i * 35);

      // Draw white rectangle for each metadata line
      img.fillRect(
        image,
        x1: x + 20,
        y1: lineY,
        x2: x + width - 20,
        y2: lineY + 25,
        color: textColor,
      );

      // Add black text representation (simple pattern)
      final text = metadata[i];
      for (int j = 0; j < text.length && j < 40; j++) {
        final charX = x + 25 + (j * 12);

        // Draw small black rectangle for each character
        img.fillRect(
          image,
          x1: charX,
          y1: lineY + 3,
          x2: charX + 10,
          y2: lineY + 22,
          color: img.ColorRgb8(0, 0, 0),
        );

        // Add character differentiation
        if (j % 3 == 0) {
          img.fillRect(
            image,
            x1: charX + 2,
            y1: lineY + 8,
            x2: charX + 8,
            y2: lineY + 17,
            color: textColor,
          );
        }
      }
    }

    // Add corner markers for absolute confirmation
    _addCornerMarkers(image);

    print('DEBUG: Simple guaranteed overlay completed');
  }

  void _addCornerMarkers(img.Image image) {
    final markerColor = img.ColorRgb8(0, 255, 0); // Bright green
    final markerSize = 50;

    // Top-left corner
    img.fillRect(image, x1: 0, y1: 0, x2: markerSize, y2: markerSize, color: markerColor);

    // Top-right corner
    img.fillRect(image, x1: image.width - markerSize, y1: 0, x2: image.width, y2: markerSize, color: markerColor);

    // Bottom-left corner
    img.fillRect(image, x1: 0, y1: image.height - markerSize, x2: markerSize, y2: image.height, color: markerColor);

    // Bottom-right corner
    img.fillRect(image, x1: image.width - markerSize, y1: image.height - markerSize, x2: image.width, y2: image.height, color: markerColor);

    print('DEBUG: Corner markers added');
  }

  void _addEmergencyOverlay(img.Image image) {
    print('DEBUG: Adding emergency overlay...');

    // Draw massive bright overlay that cannot be missed
    final emergencyColor = img.ColorRgb8(255, 0, 255); // Bright magenta

    // Draw large rectangle in center
    final centerX = image.width ~/ 2 - 200;
    final centerY = image.height ~/ 2 - 100;

    img.fillRect(
      image,
      x1: centerX,
      y1: centerY,
      x2: centerX + 400,
      y2: centerY + 200,
      color: emergencyColor,
    );

    // Add white border
    final borderColor = img.ColorRgb8(255, 255, 255);
    img.fillRect(image, x1: centerX, y1: centerY, x2: centerX + 400, y2: centerY + 20, color: borderColor);
    img.fillRect(image, x1: centerX, y1: centerY + 180, x2: centerX + 400, y2: centerY + 200, color: borderColor);
    img.fillRect(image, x1: centerX, y1: centerY, x2: centerX + 20, y2: centerY + 200, color: borderColor);
    img.fillRect(image, x1: centerX + 380, y1: centerY, x2: centerX + 400, y2: centerY + 200, color: borderColor);

    print('DEBUG: Emergency overlay completed');
  }

  bool _verifyOverlayApplied(img.Image image) {
    try {
      print('DEBUG: Verifying overlay was applied...');

      // Check for bright colors that should be present if overlay worked
      int brightPixelCount = 0;
      int totalSamples = 0;

      // Sample pixels across the image
      for (int y = 0; y < image.height; y += 50) {
        for (int x = 0; x < image.width; x += 50) {
          final pixel = image.getPixel(x, y);
          final r = pixel.r.toInt();
          final g = pixel.g.toInt();
          final b = pixel.b.toInt();

          totalSamples++;

          // Look for bright red, yellow, green, or magenta pixels
          if ((r > 200 && g < 100 && b < 100) ||  // Red
              (r > 200 && g > 200 && b < 100) ||  // Yellow
              (r < 100 && g > 200 && b < 100) ||  // Green
              (r > 200 && g < 100 && b > 200)) {  // Magenta
            brightPixelCount++;
          }
        }
      }

      final overlayPercentage = (brightPixelCount / totalSamples) * 100;
      print('DEBUG: Overlay verification: ${overlayPercentage.toStringAsFixed(1)}% bright pixels found');

      // If we found bright pixels, overlay probably worked
      final verified = overlayPercentage > 1.0; // At least 1% bright pixels

      if (verified) {
        print('SUCCESS: Overlay verified - bright pixels detected');
      } else {
        print('WARNING: Overlay verification failed - no bright pixels found');
      }

      return verified;

    } catch (e) {
      print('ERROR: Overlay verification failed: $e');
      return false;
    }
  }

  void _drawLargeTextBlocks(img.Image image, String metadata, bool isLandscape) {
    final lines = metadata.split('\n');

    // Colors
    final bgColor = img.ColorRgb8(0, 0, 0);      // Black background
    final textColor = img.ColorRgb8(255, 255, 255); // White text
    final borderColor = img.ColorRgb8(255, 0, 0);   // Red border

    // Position and size
    int x, y, maxWidth;
    if (isLandscape) {
      x = 30;
      y = 30;
      maxWidth = 300;
    } else {
      x = 30;
      y = image.height - 250;
      maxWidth = image.width - 60;
    }

    final blockHeight = 40;
    final totalHeight = lines.length * blockHeight + 40;

    print('DEBUG: Drawing text blocks at $x, $y (${maxWidth}x$totalHeight)');

    // Draw background
    img.fillRect(image, x1: x, y1: y, x2: x + maxWidth, y2: y + totalHeight, color: bgColor);

    // Draw red border (very thick)
    final borderThickness = 10;
    img.fillRect(image, x1: x, y1: y, x2: x + maxWidth, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + totalHeight - borderThickness, x2: x + maxWidth, y2: y + totalHeight, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + totalHeight, color: borderColor);
    img.fillRect(image, x1: x + maxWidth - borderThickness, y1: y, x2: x + maxWidth, y2: y + totalHeight, color: borderColor);

    // Draw text blocks (simplified approach)
    for (int i = 0; i < lines.length; i++) {
      final lineY = y + 20 + (i * blockHeight);

      // Draw white rectangle for each line
      img.fillRect(
        image,
        x1: x + 20,
        y1: lineY,
        x2: x + maxWidth - 20,
        y2: lineY + 30,
        color: textColor,
      );

      // Add line number indicator (black rectangles)
      for (int j = 0; j < (i + 1) && j < 10; j++) {
        img.fillRect(
          image,
          x1: x + 25 + (j * 25),
          y1: lineY + 5,
          x2: x + 45 + (j * 25),
          y2: lineY + 25,
          color: bgColor,
        );
      }
    }
  }

  void _addVisibilityMarkers(img.Image image, bool isLandscape) {
    // Add multiple colored markers for guaranteed visibility
    final colors = [
      img.ColorRgb8(255, 0, 0),   // Red
      img.ColorRgb8(0, 255, 0),   // Green
      img.ColorRgb8(0, 0, 255),   // Blue
      img.ColorRgb8(255, 255, 0), // Yellow
      img.ColorRgb8(255, 0, 255), // Magenta
    ];

    for (int i = 0; i < colors.length; i++) {
      int x, y;
      if (isLandscape) {
        x = image.width - 100;
        y = 50 + (i * 60);
      } else {
        x = image.width - 100;
        y = 50 + (i * 60);
      }

      // Draw colored square
      img.fillRect(
        image,
        x1: x,
        y1: y,
        x2: x + 50,
        y2: y + 50,
        color: colors[i],
      );
    }

    print('DEBUG: Added ${colors.length} visibility markers');
  }

  // Advanced Technique 1: Bitmap Text Rendering
  void _drawBitmapText(img.Image image, String metadata, bool isLandscape) {
    print('DEBUG: Drawing bitmap text...');

    final lines = metadata.split('\n');
    final bgColor = img.ColorRgb8(0, 0, 0);      // Black background
    final textColor = img.ColorRgb8(255, 255, 255); // White text
    final borderColor = img.ColorRgb8(255, 0, 0);   // Red border

    // Position and dimensions
    int x, y, maxWidth, maxHeight;
    if (isLandscape) {
      x = 50;
      y = 50;
      maxWidth = 500;
      maxHeight = 400;
    } else {
      x = 50;
      y = image.height - 450;
      maxWidth = image.width - 100;
      maxHeight = 400;
    }

    // Draw background with thick border
    img.fillRect(image, x1: x, y1: y, x2: x + maxWidth, y2: y + maxHeight, color: bgColor);

    // Draw thick red border
    final borderThickness = 8;
    img.fillRect(image, x1: x, y1: y, x2: x + maxWidth, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + maxHeight - borderThickness, x2: x + maxWidth, y2: y + maxHeight, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + maxHeight, color: borderColor);
    img.fillRect(image, x1: x + maxWidth - borderThickness, y1: y, x2: x + maxWidth, y2: y + maxHeight, color: borderColor);

    // Draw text using bitmap font approach
    for (int lineIndex = 0; lineIndex < lines.length && lineIndex < 8; lineIndex++) {
      final line = lines[lineIndex];
      final lineY = y + 30 + (lineIndex * 40);

      // Draw line background
      img.fillRect(
        image,
        x1: x + 20,
        y1: lineY - 5,
        x2: x + maxWidth - 20,
        y2: lineY + 30,
        color: textColor,
      );

      // Draw text using bitmap patterns
      _drawBitmapTextLine(image, line, x + 25, lineY, bgColor);
    }

    print('DEBUG: Bitmap text rendering completed');
  }

  void _drawBitmapTextLine(img.Image image, String text, int startX, int startY, img.ColorRgb8 textColor) {
    // Simple bitmap font - each character is 8x12 pixels
    final charWidth = 8;
    final charHeight = 12;

    for (int i = 0; i < text.length && i < 50; i++) {
      final char = text[i].toUpperCase();
      final charX = startX + (i * charWidth);

      // Draw character bitmap
      _drawCharacterBitmap(image, char, charX, startY, charWidth, charHeight, textColor);
    }
  }

  void _drawCharacterBitmap(img.Image image, String char, int x, int y, int width, int height, img.ColorRgb8 color) {
    // Define bitmap patterns for common characters
    final patterns = _getCharacterPattern(char);

    for (int row = 0; row < patterns.length && row < height; row++) {
      final pattern = patterns[row];
      for (int col = 0; col < pattern.length && col < width; col++) {
        if (pattern[col] == '1') {
          // Draw pixel
          img.fillRect(
            image,
            x1: x + col,
            y1: y + row,
            x2: x + col + 1,
            y2: y + row + 1,
            color: color,
          );
        }
      }
    }
  }

  List<String> _getCharacterPattern(String char) {
    // Simple 8x12 bitmap patterns for characters
    switch (char) {
      case 'A':
        return [
          '  1111  ',
          ' 11  11 ',
          '11    11',
          '11    11',
          '11111111',
          '11    11',
          '11    11',
          '11    11',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'B':
        return [
          '1111111 ',
          '11    11',
          '11    11',
          '1111111 ',
          '1111111 ',
          '11    11',
          '11    11',
          '1111111 ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'C':
        return [
          ' 1111111',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          ' 1111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'E':
        return [
          '11111111',
          '11      ',
          '11      ',
          '1111111 ',
          '1111111 ',
          '11      ',
          '11      ',
          '11111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'F':
        return [
          '11111111',
          '11      ',
          '11      ',
          '1111111 ',
          '1111111 ',
          '11      ',
          '11      ',
          '11      ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'I':
        return [
          '11111111',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '11111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'L':
        return [
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '11111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'N':
        return [
          '11    11',
          '111   11',
          '1111  11',
          '11 11 11',
          '11  1111',
          '11   111',
          '11    11',
          '11    11',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'O':
        return [
          ' 1111111',
          '11    11',
          '11    11',
          '11    11',
          '11    11',
          '11    11',
          '11    11',
          ' 1111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'P':
        return [
          '1111111 ',
          '11    11',
          '11    11',
          '1111111 ',
          '11      ',
          '11      ',
          '11      ',
          '11      ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'R':
        return [
          '1111111 ',
          '11    11',
          '11    11',
          '1111111 ',
          '11 11   ',
          '11  11  ',
          '11   11 ',
          '11    11',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case 'T':
        return [
          '11111111',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '   11   ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case ':':
        return [
          '        ',
          '        ',
          '   11   ',
          '   11   ',
          '        ',
          '        ',
          '   11   ',
          '   11   ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      case ' ':
        return [
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
      default:
        // Default pattern for unknown characters
        return [
          '11111111',
          '11111111',
          '11111111',
          '11111111',
          '11111111',
          '11111111',
          '11111111',
          '11111111',
          '        ',
          '        ',
          '        ',
          '        ',
        ];
    }
  }

  // Advanced Technique 2: Simple Data Matrix (QR-like pattern)
  Future<void> _addMetadataQRCode(img.Image image, String metadata, bool isLandscape) async {
    try {
      print('DEBUG: Generating data matrix for metadata...');

      // Position for data matrix
      int qrX, qrY;
      if (isLandscape) {
        qrX = image.width - 200;
        qrY = 50;
      } else {
        qrX = image.width - 200;
        qrY = 50;
      }

      final matrixSize = 150;

      // Draw white background
      img.fillRect(
        image,
        x1: qrX - 10,
        y1: qrY - 10,
        x2: qrX + matrixSize + 10,
        y2: qrY + matrixSize + 10,
        color: img.ColorRgb8(255, 255, 255),
      );

      // Create simple data pattern based on metadata hash
      final metadataHash = metadata.hashCode;
      final gridSize = 15; // 15x15 grid
      final cellSize = matrixSize ~/ gridSize;

      // Draw data pattern
      for (int row = 0; row < gridSize; row++) {
        for (int col = 0; col < gridSize; col++) {
          // Create pattern based on metadata hash and position
          final cellValue = (metadataHash + row * gridSize + col) % 3;

          if (cellValue == 0) {
            // Black cell
            img.fillRect(
              image,
              x1: qrX + (col * cellSize),
              y1: qrY + (row * cellSize),
              x2: qrX + ((col + 1) * cellSize),
              y2: qrY + ((row + 1) * cellSize),
              color: img.ColorRgb8(0, 0, 0),
            );
          }
          // White cells remain white (background)
        }
      }

      // Add border pattern
      img.fillRect(image, x1: qrX, y1: qrY, x2: qrX + matrixSize, y2: qrY + 5, color: img.ColorRgb8(0, 0, 0));
      img.fillRect(image, x1: qrX, y1: qrY + matrixSize - 5, x2: qrX + matrixSize, y2: qrY + matrixSize, color: img.ColorRgb8(0, 0, 0));
      img.fillRect(image, x1: qrX, y1: qrY, x2: qrX + 5, y2: qrY + matrixSize, color: img.ColorRgb8(0, 0, 0));
      img.fillRect(image, x1: qrX + matrixSize - 5, y1: qrY, x2: qrX + matrixSize, y2: qrY + matrixSize, color: img.ColorRgb8(0, 0, 0));

      print('DEBUG: Data matrix generated successfully');

    } catch (e) {
      print('ERROR: Data matrix generation failed: $e');
      // Continue with other methods
    }
  }

  // Advanced Technique 3: Color-Coded Metadata Blocks
  void _addColorCodedMetadata(img.Image image, AppStateProvider appStateProvider, bool isLandscape) {
    print('DEBUG: Adding color-coded metadata blocks...');

    // Define colors for each metadata field
    final fieldColors = [
      img.ColorRgb8(255, 0, 0),   // Red - Project
      img.ColorRgb8(0, 255, 0),   // Green - Beneficiary
      img.ColorRgb8(0, 0, 255),   // Blue - G.P.
      img.ColorRgb8(255, 255, 0), // Yellow - Location
      img.ColorRgb8(255, 0, 255), // Magenta - Note
      img.ColorRgb8(0, 255, 255), // Cyan - GPS
      img.ColorRgb8(128, 128, 128), // Gray - Date
    ];

    final fields = [
      appStateProvider.projectName,
      appStateProvider.beneficiaryName,
      appStateProvider.gp,
      appStateProvider.location,
      appStateProvider.note,
      '${appStateProvider.latitude ?? 0}, ${appStateProvider.longitude ?? 0}',
      appStateProvider.captureDateTime?.toString() ?? DateTime.now().toString(),
    ];

    // Position for color blocks
    int startX, startY;
    if (isLandscape) {
      startX = 50;
      startY = image.height - 150;
    } else {
      startX = 50;
      startY = 50;
    }

    // Draw color-coded blocks
    for (int i = 0; i < fields.length && i < fieldColors.length; i++) {
      final field = fields[i];
      if (field.isNotEmpty) {
        final blockX = startX + (i * 80);
        final blockY = startY;

        // Draw colored block
        img.fillRect(
          image,
          x1: blockX,
          y1: blockY,
          x2: blockX + 70,
          y2: blockY + 70,
          color: fieldColors[i],
        );

        // Draw white border
        img.fillRect(image, x1: blockX, y1: blockY, x2: blockX + 70, y2: blockY + 5, color: img.ColorRgb8(255, 255, 255));
        img.fillRect(image, x1: blockX, y1: blockY + 65, x2: blockX + 70, y2: blockY + 70, color: img.ColorRgb8(255, 255, 255));
        img.fillRect(image, x1: blockX, y1: blockY, x2: blockX + 5, y2: blockY + 70, color: img.ColorRgb8(255, 255, 255));
        img.fillRect(image, x1: blockX + 65, y1: blockY, x2: blockX + 70, y2: blockY + 70, color: img.ColorRgb8(255, 255, 255));

        // Add field indicator (number of dots = field length / 10)
        final dotCount = (field.length / 10).ceil().clamp(1, 7);
        for (int j = 0; j < dotCount; j++) {
          final dotX = blockX + 10 + (j * 8);
          final dotY = blockY + 30;

          img.fillRect(
            image,
            x1: dotX,
            y1: dotY,
            x2: dotX + 6,
            y2: dotY + 6,
            color: img.ColorRgb8(255, 255, 255),
          );
        }
      }
    }

    print('DEBUG: Color-coded metadata blocks completed');
  }

  // Advanced Technique 4: Steganographic Metadata Embedding
  void _embedMetadataInPixels(img.Image image, String metadata) {
    try {
      print('DEBUG: Embedding metadata in image pixels...');

      // Convert metadata to binary
      final metadataBytes = utf8.encode(metadata);
      final binaryData = <int>[];

      // Add length header (4 bytes)
      final length = metadataBytes.length;
      binaryData.addAll([
        (length >> 24) & 0xFF,
        (length >> 16) & 0xFF,
        (length >> 8) & 0xFF,
        length & 0xFF,
      ]);

      // Add metadata bytes
      binaryData.addAll(metadataBytes);

      // Convert to binary string
      final binaryString = binaryData.map((byte) =>
        byte.toRadixString(2).padLeft(8, '0')).join();

      print('DEBUG: Binary data length: ${binaryString.length} bits');

      // Embed in least significant bits of red channel
      int bitIndex = 0;
      bool embedded = false;

      for (int y = 0; y < image.height && !embedded; y++) {
        for (int x = 0; x < image.width && !embedded; x++) {
          if (bitIndex < binaryString.length) {
            final pixel = image.getPixel(x, y);
            final r = pixel.r.toInt();
            final g = pixel.g.toInt();
            final b = pixel.b.toInt();

            // Modify least significant bit of red channel
            final bit = int.parse(binaryString[bitIndex]);
            final newR = (r & 0xFE) | bit; // Clear LSB and set new bit

            // Set modified pixel
            image.setPixel(x, y, img.ColorRgb8(newR, g, b));
            bitIndex++;
          } else {
            embedded = true;
          }
        }
      }

      print('DEBUG: Metadata embedded in $bitIndex pixels');

    } catch (e) {
      print('ERROR: Steganographic embedding failed: $e');
      // Continue with other methods
    }
  }

  // Method to extract embedded metadata (for verification)
  String? _extractMetadataFromPixels(img.Image image) {
    try {
      print('DEBUG: Extracting metadata from pixels...');

      // Extract length header (32 bits)
      final lengthBits = <String>[];
      for (int i = 0; i < 32; i++) {
        final x = i % image.width;
        final y = i ~/ image.width;
        final pixel = image.getPixel(x, y);
        lengthBits.add((pixel.r.toInt() & 1).toString());
      }

      final lengthBinary = lengthBits.join();
      final length = int.parse(lengthBinary, radix: 2);

      if (length <= 0 || length > 10000) {
        print('DEBUG: Invalid metadata length: $length');
        return null;
      }

      // Extract metadata bits
      final metadataBits = <String>[];
      for (int i = 32; i < 32 + (length * 8); i++) {
        final x = i % image.width;
        final y = i ~/ image.width;
        if (y >= image.height) break;

        final pixel = image.getPixel(x, y);
        metadataBits.add((pixel.r.toInt() & 1).toString());
      }

      // Convert bits to bytes
      final bytes = <int>[];
      for (int i = 0; i < metadataBits.length; i += 8) {
        if (i + 7 < metadataBits.length) {
          final byteBits = metadataBits.sublist(i, i + 8).join();
          bytes.add(int.parse(byteBits, radix: 2));
        }
      }

      final extractedMetadata = utf8.decode(bytes);
      print('DEBUG: Extracted metadata: $extractedMetadata');
      return extractedMetadata;

    } catch (e) {
      print('ERROR: Metadata extraction failed: $e');
      return null;
    }
  }

  void _addGuaranteedFallbackOverlay(img.Image image, AppStateProvider appStateProvider) {
    print('DEBUG: Adding guaranteed fallback overlay...');

    // This overlay WILL be visible - uses only basic rectangle drawing
    final isLandscape = image.width > image.height;

    // Bright colors that are impossible to miss
    final bgColor = img.ColorRgb8(255, 0, 255);    // Bright magenta
    final borderColor = img.ColorRgb8(255, 255, 0); // Bright yellow
    final textColor = img.ColorRgb8(0, 0, 0);       // Black

    int x, y, width, height;
    if (isLandscape) {
      x = 50;
      y = 50;
      width = 400;
      height = 300;
    } else {
      x = 50;
      y = image.height - 350;
      width = image.width - 100;
      height = 300;
    }

    // Draw magenta background
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + height, color: bgColor);

    // Draw yellow border (very thick)
    final borderThickness = 15;
    img.fillRect(image, x1: x, y1: y, x2: x + width, y2: y + borderThickness, color: borderColor);
    img.fillRect(image, x1: x, y1: y + height - borderThickness, x2: x + width, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x, y1: y, x2: x + borderThickness, y2: y + height, color: borderColor);
    img.fillRect(image, x1: x + width - borderThickness, y1: y, x2: x + width, y2: y + height, color: borderColor);

    // Add simple text representation using black rectangles
    final textLines = [
      'PROJECT: ${appStateProvider.projectName}',
      'BENEFICIARY: ${appStateProvider.beneficiaryName}',
      'GP: ${appStateProvider.gp}',
      'LOCATION: ${appStateProvider.location}',
      'NOTE: ${appStateProvider.note}',
    ];

    for (int i = 0; i < textLines.length && i < 5; i++) {
      final lineY = y + 40 + (i * 45);

      // Draw black rectangle for text line
      img.fillRect(
        image,
        x1: x + 20,
        y1: lineY,
        x2: x + width - 20,
        y2: lineY + 35,
        color: textColor,
      );

      // Add yellow dots to indicate line number
      for (int j = 0; j <= i && j < 5; j++) {
        img.fillRect(
          image,
          x1: x + 30 + (j * 40),
          y1: lineY + 5,
          x2: x + 50 + (j * 40),
          y2: lineY + 25,
          color: borderColor,
        );
      }
    }

    print('DEBUG: Guaranteed fallback overlay completed');
  }

  // Method 2: Sidecar JSON File
  Future<void> _createMetadataSidecarFile(
    AppStateProvider appStateProvider,
    String filename,
  ) async {
    try {
      print('DEBUG: Creating metadata sidecar file...');

      // Create comprehensive metadata JSON
      final metadataJson = {
        'version': '1.0',
        'app': 'PCam Professional Camera',
        'timestamp': DateTime.now().toIso8601String(),
        'filename': filename,
        'metadata': {
          'projectName': appStateProvider.projectName,
          'beneficiaryName': appStateProvider.beneficiaryName,
          'gp': appStateProvider.gp,
          'location': appStateProvider.location,
          'note': appStateProvider.note,
          'latitude': appStateProvider.latitude,
          'longitude': appStateProvider.longitude,
          'captureDateTime': appStateProvider.captureDateTime?.toIso8601String(),
        },
        'technical': {
          'imageWidth': null, // Will be filled by caller
          'imageHeight': null, // Will be filled by caller
          'orientation': null, // Will be filled by caller
        }
      };

      // Save to external storage
      final directory = await getExternalStorageDirectory();
      if (directory != null) {
        final metadataDir = Directory('${directory.path}/PCam/metadata');
        await metadataDir.create(recursive: true);

        final jsonFilename = filename.replaceAll('.jpg', '.json');
        final jsonFile = File('${metadataDir.path}/$jsonFilename');

        await jsonFile.writeAsString(
          const JsonEncoder.withIndent('  ').convert(metadataJson),
        );

        print('DEBUG: Metadata sidecar file created: ${jsonFile.path}');
      }

      // Also save to app documents directory as backup
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/metadata');
      await backupDir.create(recursive: true);

      final jsonFilename = filename.replaceAll('.jpg', '.json');
      final backupFile = File('${backupDir.path}/$jsonFilename');

      await backupFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(metadataJson),
      );

      print('DEBUG: Metadata backup file created: ${backupFile.path}');

    } catch (e) {
      print('ERROR: Failed to create sidecar file: $e');
      // This is not critical, continue with other methods
    }
  }
}

class Point<T> {
  final T x;
  final T y;
  
  Point(this.x, this.y);
}
