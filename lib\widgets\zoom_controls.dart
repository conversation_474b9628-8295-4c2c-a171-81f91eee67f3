import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/camera_provider.dart';
import 'zoom_wheel.dart';

class ZoomControls extends StatefulWidget {
  const ZoomControls({super.key});

  @override
  State<ZoomControls> createState() => _ZoomControlsState();
}

class _ZoomControlsState extends State<ZoomControls>
    with TickerProviderStateMixin {
  bool _showZoomWheel = false;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.5, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CameraProvider>(
      builder: (context, cameraProvider, child) {
        if (!cameraProvider.isInitialized) {
          return const SizedBox.shrink();
        }

        return Stack(
          children: [
            // Zoom wheel (when visible)
            if (_showZoomWheel)
              Positioned(
                right: 20,
                top: MediaQuery.of(context).size.height / 2 - 60,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: ZoomWheel(isVisible: _showZoomWheel),
                ),
              ),
            
            // Zoom controls panel
            Positioned(
              right: 16,
              top: MediaQuery.of(context).size.height / 2 - 100,
              child: _buildZoomControlsPanel(cameraProvider),
            ),
          ],
        );
      },
    );
  }

  Widget _buildZoomControlsPanel(CameraProvider cameraProvider) {
    return Column(
      children: [
        // Zoom wheel toggle button
        _buildZoomWheelToggle(),
        
        const SizedBox(height: 12),
        
        // Quick zoom buttons
        _buildQuickZoomButtons(cameraProvider),
        
        const SizedBox(height: 12),
        
        // Zoom slider (vertical)
        _buildVerticalZoomSlider(cameraProvider),
      ],
    );
  }

  Widget _buildZoomWheelToggle() {
    return GestureDetector(
      onTap: _toggleZoomWheel,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _showZoomWheel
                ? [Colors.blue.withOpacity(0.8), Colors.cyan.withOpacity(0.8)]
                : [Colors.white.withOpacity(0.2), Colors.white.withOpacity(0.1)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          _showZoomWheel ? Icons.radio_button_checked : Icons.radio_button_unchecked,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildQuickZoomButtons(CameraProvider cameraProvider) {
    final quickZoomLevels = [1.0, 2.0, 5.0, 10.0, 25.0, 50.0, 100.0];
    
    return Column(
      children: quickZoomLevels
          .where((level) => level <= cameraProvider.maxZoom)
          .map((level) => _buildQuickZoomButton(level, cameraProvider))
          .toList(),
    );
  }

  Widget _buildQuickZoomButton(double zoomLevel, CameraProvider cameraProvider) {
    final isActive = (cameraProvider.zoomLevel - zoomLevel).abs() < 0.1;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: GestureDetector(
        onTap: () => cameraProvider.setZoomLevel(zoomLevel),
        child: Container(
          width: 40,
          height: 32,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isActive
                  ? [Colors.blue.withOpacity(0.8), Colors.cyan.withOpacity(0.8)]
                  : [Colors.white.withOpacity(0.2), Colors.white.withOpacity(0.1)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isActive 
                  ? Colors.white.withOpacity(0.8)
                  : Colors.white.withOpacity(0.3),
              width: isActive ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: isActive ? 8 : 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              zoomLevel >= 10 ? '${zoomLevel.toInt()}' : '${zoomLevel.toInt()}x',
              style: TextStyle(
                color: Colors.white,
                fontSize: zoomLevel >= 10 ? 10 : 11,
                fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalZoomSlider(CameraProvider cameraProvider) {
    return Container(
      height: 120,
      width: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.2),
            Colors.white.withOpacity(0.1),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: RotatedBox(
        quarterTurns: 3, // Rotate 270 degrees to make it vertical
        child: SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 6,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            activeTrackColor: Colors.blue.withOpacity(0.8),
            inactiveTrackColor: Colors.white.withOpacity(0.3),
            thumbColor: Colors.white,
            overlayColor: Colors.blue.withOpacity(0.2),
          ),
          child: Slider(
            value: cameraProvider.zoomLevel,
            min: cameraProvider.minZoom,
            max: cameraProvider.maxZoom,
            divisions: 100,
            onChanged: (value) {
              cameraProvider.setZoomLevel(value);
            },
          ),
        ),
      ),
    );
  }

  void _toggleZoomWheel() {
    setState(() {
      _showZoomWheel = !_showZoomWheel;
    });
    
    if (_showZoomWheel) {
      _slideController.forward();
    } else {
      _slideController.reverse();
    }
  }
}

class ZoomIndicator extends StatelessWidget {
  const ZoomIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<CameraProvider>(
      builder: (context, cameraProvider, child) {
        if (!cameraProvider.isInitialized || cameraProvider.zoomLevel <= 1.0) {
          return const SizedBox.shrink();
        }

        return Positioned(
          top: MediaQuery.of(context).padding.top + 120,
          right: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.black.withOpacity(0.8),
                  Colors.black.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.zoom_in,
                  color: _getZoomColor(cameraProvider.zoomLevel),
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '${cameraProvider.zoomLevel.toStringAsFixed(1)}x',
                  style: TextStyle(
                    color: _getZoomColor(cameraProvider.zoomLevel),
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getZoomColor(double zoomLevel) {
    if (zoomLevel < 2) return Colors.green;
    if (zoomLevel < 5) return Colors.blue;
    if (zoomLevel < 10) return Colors.orange;
    if (zoomLevel < 25) return Colors.red;
    return Colors.purple;
  }
}
