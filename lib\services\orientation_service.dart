import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';

enum SensorOrientation {
  portrait,
  landscapeLeft,
  landscapeRight,
  portraitUpsideDown,
}

enum MetadataOrientation {
  portrait,
  landscape,
}

class OrientationService extends ChangeNotifier {
  static final OrientationService _instance = OrientationService._internal();
  factory OrientationService() => _instance;
  OrientationService._internal();

  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  SensorOrientation _currentOrientation = SensorOrientation.portrait;
  MetadataOrientation _metadataOrientation = MetadataOrientation.portrait;
  
  // Smoothing variables
  double _smoothedX = 0.0;
  double _smoothedY = 0.0;
  double _smoothedZ = 0.0;
  static const double _smoothingFactor = 0.1;
  
  // Threshold for orientation detection
  static const double _orientationThreshold = 5.0;
  
  // Getters
  SensorOrientation get currentOrientation => _currentOrientation;
  MetadataOrientation get metadataOrientation => _metadataOrientation;
  bool get isLandscape => _metadataOrientation == MetadataOrientation.landscape;
  bool get isPortrait => _metadataOrientation == MetadataOrientation.portrait;
  
  String get orientationDescription {
    switch (_currentOrientation) {
      case SensorOrientation.portrait:
        return 'Portrait (Normal)';
      case SensorOrientation.landscapeLeft:
        return 'Landscape (Left)';
      case SensorOrientation.landscapeRight:
        return 'Landscape (Right)';
      case SensorOrientation.portraitUpsideDown:
        return 'Portrait (Upside Down)';
    }
  }

  /// Initialize the orientation service
  void initialize() {
    _startListening();
  }

  /// Start listening to accelerometer data
  void _startListening() {
    _accelerometerSubscription = accelerometerEvents.listen(
      _onAccelerometerEvent,
      onError: (error) {
        print('Accelerometer error: $error');
      },
    );
  }

  /// Handle accelerometer events
  void _onAccelerometerEvent(AccelerometerEvent event) {
    // Apply smoothing to reduce noise
    _smoothedX = _smoothedX + _smoothingFactor * (event.x - _smoothedX);
    _smoothedY = _smoothedY + _smoothingFactor * (event.y - _smoothedY);
    _smoothedZ = _smoothedZ + _smoothingFactor * (event.z - _smoothedZ);

    // Calculate orientation based on smoothed accelerometer data
    final newOrientation = _calculateOrientation(_smoothedX, _smoothedY, _smoothedZ);
    
    if (newOrientation != _currentOrientation) {
      _currentOrientation = newOrientation;
      _updateMetadataOrientation();
      
      print('📱 Orientation changed: ${orientationDescription}');
      print('📍 Metadata orientation: ${_metadataOrientation.name}');
      
      notifyListeners();
    }
  }

  /// Calculate device orientation from accelerometer data
  SensorOrientation _calculateOrientation(double x, double y, double z) {
    // Calculate the magnitude of the gravity vector
    final magnitude = sqrt(x * x + y * y + z * z);
    
    // Normalize the values
    final normalizedX = x / magnitude;
    final normalizedY = y / magnitude;
    
    // Determine orientation based on the dominant axis
    if (normalizedY.abs() > normalizedX.abs()) {
      // Vertical orientations
      if (normalizedY > _orientationThreshold / 10) {
        return SensorOrientation.portraitUpsideDown;
      } else {
        return SensorOrientation.portrait;
      }
    } else {
      // Horizontal orientations
      if (normalizedX > _orientationThreshold / 10) {
        return SensorOrientation.landscapeRight;
      } else {
        return SensorOrientation.landscapeLeft;
      }
    }
  }

  /// Update metadata orientation based on device orientation
  void _updateMetadataOrientation() {
    switch (_currentOrientation) {
      case SensorOrientation.portrait:
      case SensorOrientation.portraitUpsideDown:
        _metadataOrientation = MetadataOrientation.portrait;
        break;
      case SensorOrientation.landscapeLeft:
      case SensorOrientation.landscapeRight:
        _metadataOrientation = MetadataOrientation.landscape;
        break;
    }
  }

  /// Get metadata position based on current orientation
  String getMetadataPosition() {
    return isLandscape ? 'Top Left' : 'Bottom Left';
  }

  /// Get detailed orientation info for debugging
  Map<String, dynamic> getOrientationInfo() {
    return {
      'deviceOrientation': _currentOrientation.name,
      'metadataOrientation': _metadataOrientation.name,
      'isLandscape': isLandscape,
      'isPortrait': isPortrait,
      'position': getMetadataPosition(),
      'smoothedX': _smoothedX.toStringAsFixed(2),
      'smoothedY': _smoothedY.toStringAsFixed(2),
      'smoothedZ': _smoothedZ.toStringAsFixed(2),
    };
  }

  /// Force refresh orientation (useful for manual checks)
  void refreshOrientation() {
    notifyListeners();
  }

  /// Stop listening to sensors
  void dispose() {
    _accelerometerSubscription?.cancel();
    _accelerometerSubscription = null;
    super.dispose();
  }

  /// Check if sensors are available
  static Future<bool> areSensorsAvailable() async {
    try {
      // Try to get a single accelerometer reading
      final completer = Completer<bool>();
      late StreamSubscription subscription;
      
      subscription = accelerometerEvents.listen(
        (event) {
          subscription.cancel();
          completer.complete(true);
        },
        onError: (error) {
          subscription.cancel();
          completer.complete(false);
        },
      );
      
      // Timeout after 2 seconds
      Timer(const Duration(seconds: 2), () {
        if (!completer.isCompleted) {
          subscription.cancel();
          completer.complete(false);
        }
      });
      
      return await completer.future;
    } catch (e) {
      return false;
    }
  }
}
