import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/camera_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/app_state_provider.dart';
import '../services/orientation_service.dart';
import '../widgets/camera_preview_widget.dart';
import '../widgets/camera_controls.dart';
import '../widgets/metadata_input_dialog.dart';
import '../widgets/settings_panel.dart';
import '../widgets/orientation_indicator.dart';
import '../widgets/zoom_controls.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
    _loadSettings();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final cameraProvider = Provider.of<CameraProvider>(context, listen: false);

    switch (state) {
      case AppLifecycleState.paused:
        // App is paused (minimized)
        cameraProvider.pauseCamera();
        break;
      case AppLifecycleState.resumed:
        // App is resumed
        _reinitializeCamera();
        break;
      case AppLifecycleState.inactive:
        // App is inactive (during transitions)
        break;
      case AppLifecycleState.detached:
        // App is detached
        cameraProvider.dispose();
        break;
      case AppLifecycleState.hidden:
        // App is hidden
        cameraProvider.pauseCamera();
        break;
    }
  }

  Future<void> _reinitializeCamera() async {
    final cameraProvider = Provider.of<CameraProvider>(context, listen: false);
    await cameraProvider.reinitializeCamera();
  }

  Future<void> _initializeCamera() async {
    final cameraProvider = Provider.of<CameraProvider>(context, listen: false);
    await cameraProvider.initializeCamera();
  }

  Future<void> _loadSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    await settingsProvider.loadSettings();
  }

  void _showMetadataDialog() {
    showDialog(
      context: context,
      builder: (context) => const MetadataInputDialog(),
    );
  }

  void _showSettingsPanel() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SettingsPanel(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      body: Consumer3<CameraProvider, SettingsProvider, AppStateProvider>(
        builder: (context, cameraProvider, settingsProvider, appStateProvider, child) {
          if (cameraProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    cameraProvider.errorMessage!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      cameraProvider.clearError();
                      _initializeCamera();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (!cameraProvider.isInitialized) {
            return const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
              ),
            );
          }

          return Stack(
            children: [
              // Camera preview
              const CameraPreviewWidget(),
              
              // Top controls
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                left: 16,
                right: 16,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Flash control with glassmorphism
                    _buildGlassmorphicTopButton(
                      icon: _getFlashIcon(cameraProvider.flashMode),
                      onPressed: () {
                        final currentFlash = cameraProvider.flashMode;
                        FlashMode nextFlash;
                        switch (currentFlash) {
                          case FlashMode.auto:
                            nextFlash = FlashMode.on;
                            break;
                          case FlashMode.on:
                            nextFlash = FlashMode.off;
                            break;
                          case FlashMode.off:
                            nextFlash = FlashMode.auto;
                            break;
                        }
                        cameraProvider.setFlashMode(nextFlash);
                      },
                    ),

                    // Settings button with glassmorphism
                    _buildGlassmorphicTopButton(
                      icon: Icons.settings,
                      onPressed: _showSettingsPanel,
                    ),
                  ],
                ),
              ),

              // Orientation indicator
              Positioned(
                top: MediaQuery.of(context).padding.top + 80,
                left: 16,
                child: const OrientationIndicator(showDetailed: true),
              ),

              // Zoom controls
              const Positioned.fill(
                child: ZoomControls(),
              ),

              // Zoom indicator
              const ZoomIndicator(),
              
              // Bottom controls
              Positioned(
                bottom: MediaQuery.of(context).padding.bottom + 16,
                left: 0,
                right: 0,
                child: const CameraControls(),
              ),

              // Enhanced Floating metadata button
              Positioned(
                bottom: MediaQuery.of(context).padding.bottom + 100,
                right: 16,
                child: TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 300),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        width: 56,
                        height: 56,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFF667eea),
                              Color(0xFF764ba2),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(28),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF667eea).withOpacity(0.4),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(28),
                            onTap: _showMetadataDialog,
                            child: const Center(
                              child: Icon(
                                Icons.edit_note,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildGlassmorphicTopButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.25),
            Colors.white.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24),
          onTap: onPressed,
          child: Center(
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  IconData _getFlashIcon(FlashMode flashMode) {
    switch (flashMode) {
      case FlashMode.auto:
        return Icons.flash_auto;
      case FlashMode.on:
        return Icons.flash_on;
      case FlashMode.off:
        return Icons.flash_off;
    }
  }
}
