import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../services/location_service.dart';

class MetadataInputDialog extends StatefulWidget {
  const MetadataInputDialog({super.key});

  @override
  State<MetadataInputDialog> createState() => _MetadataInputDialogState();
}

class _MetadataInputDialogState extends State<MetadataInputDialog> {
  final _formKey = GlobalKey<FormState>();
  final _projectController = TextEditingController();
  final _beneficiaryController = TextEditingController();
  final _gpController = TextEditingController();
  final _locationController = TextEditingController();
  final _noteController = TextEditingController();

  bool _isLoadingLocation = false;
  final LocationService _locationService = LocationService();

  @override
  void initState() {
    super.initState();
    _loadCurrentValues();
  }

  @override
  void dispose() {
    _projectController.dispose();
    _beneficiaryController.dispose();
    _gpController.dispose();
    _locationController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  void _loadCurrentValues() {
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    _projectController.text = appStateProvider.projectName;
    _beneficiaryController.text = appStateProvider.beneficiaryName;
    _gpController.text = appStateProvider.gp;
    _locationController.text = appStateProvider.location;
    _noteController.text = appStateProvider.note;
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final position = await _locationService.getCurrentPosition();
      if (position != null) {
        final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
        appStateProvider.setCoordinates(position.latitude, position.longitude);

        // Try to get address from coordinates
        final address = await _locationService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (address != null && address.isNotEmpty) {
          _locationController.text = address;
        } else {
          _locationController.text = 'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}';
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Location updated successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to get location. Please check permissions.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  void _saveMetadata() {
    if (_formKey.currentState!.validate()) {
      final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);

      appStateProvider.setProjectName(_projectController.text.trim());
      appStateProvider.setBeneficiaryName(_beneficiaryController.text.trim());
      appStateProvider.setGP(_gpController.text.trim());
      appStateProvider.setLocation(_locationController.text.trim());
      appStateProvider.setNote(_noteController.text.trim());

      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Metadata updated successfully!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.7 + (0.3 * value),
          child: Opacity(
            opacity: value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFffecd2),
                      Color(0xFFfcb69f),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Enhanced Title
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: const Text(
                  '📸 Photo Metadata',
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2c3e50),
                    letterSpacing: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),

              // Enhanced Project Name
              _buildStyledTextField(
                controller: _projectController,
                labelText: '🏗 Project Name',
                hintText: 'Enter project name',
                icon: Icons.work,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Project name is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Enhanced Beneficiary Name
              _buildStyledTextField(
                controller: _beneficiaryController,
                labelText: '🧑‍💼 Beneficiary Name',
                hintText: 'Enter beneficiary name',
                icon: Icons.person,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Beneficiary name is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Enhanced G.P. Field
              _buildStyledTextField(
                controller: _gpController,
                labelText: '🗺 G.P.',
                hintText: 'Enter Gram Panchayat name',
                icon: Icons.account_balance,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'G.P. name is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Enhanced Location Field
              _buildStyledTextField(
                controller: _locationController,
                labelText: '📍 Location',
                hintText: 'Enter location or get current location',
                icon: Icons.location_on,
                suffixIcon: IconButton(
                  onPressed: _isLoadingLocation ? null : _getCurrentLocation,
                  icon: _isLoadingLocation
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
                          ),
                        )
                      : const Icon(
                          Icons.my_location,
                          color: Color(0xFF667eea),
                        ),
                  tooltip: 'Get current location',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Location is required';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Enhanced Note Field
              _buildStyledTextField(
                controller: _noteController,
                labelText: '📝 Note',
                hintText: 'Enter additional notes (optional)',
                icon: Icons.note_alt,
                validator: null, // Note is optional
              ),
              const SizedBox(height: 24),

              // GPS Coordinates Display
              Consumer<AppStateProvider>(
                builder: (context, appStateProvider, child) {
                  if (appStateProvider.latitude != null && appStateProvider.longitude != null) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '📍 GPS Coordinates:',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Lat: ${appStateProvider.latitude!.toStringAsFixed(6)}',
                            style: const TextStyle(fontSize: 12),
                          ),
                          Text(
                            'Lng: ${appStateProvider.longitude!.toStringAsFixed(6)}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveMetadata,
                      child: const Text('Save'),
                    ),
                  ),
                ],
              ),
            ],
          ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData icon,
    String? Function(String?)? validator,
    Widget? suffixIcon,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        validator: validator,
        style: const TextStyle(
          color: Color(0xFF2c3e50),
          fontSize: 16,
        ),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          prefixIcon: Icon(
            icon,
            color: const Color(0xFF667eea),
          ),
          suffixIcon: suffixIcon,
          labelStyle: const TextStyle(
            color: Color(0xFF667eea),
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: const Color(0xFF2c3e50).withOpacity(0.6),
          ),
          filled: true,
          fillColor: Colors.white.withOpacity(0.9),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: const Color(0xFF667eea).withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF667eea),
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }
}
