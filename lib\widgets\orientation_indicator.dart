import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/orientation_service.dart';

class OrientationIndicator extends StatelessWidget {
  final bool showDetailed;
  
  const OrientationIndicator({
    super.key,
    this.showDetailed = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<OrientationService>(
      builder: (context, orientationService, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.black.withOpacity(0.8),
                Colors.black.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: orientationService.isLandscape 
                  ? Colors.green.withOpacity(0.8)
                  : Colors.blue.withOpacity(0.8),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Orientation icon
              Icon(
                _getOrientationIcon(orientationService.currentOrientation),
                color: orientationService.isLandscape 
                    ? Colors.green
                    : Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              
              // Orientation text
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    orientationService.orientationDescription,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Metadata: ${orientationService.getMetadataPosition()}',
                    style: TextStyle(
                      color: orientationService.isLandscape 
                          ? Colors.green.shade300
                          : Colors.blue.shade300,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              // Detailed info (if enabled)
              if (showDetailed) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () => _showDetailedInfo(context, orientationService),
                  child: Icon(
                    Icons.info_outline,
                    color: Colors.white.withOpacity(0.7),
                    size: 16,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  IconData _getOrientationIcon(SensorOrientation orientation) {
    switch (orientation) {
      case SensorOrientation.portrait:
        return Icons.stay_current_portrait;
      case SensorOrientation.landscapeLeft:
        return Icons.stay_current_landscape;
      case SensorOrientation.landscapeRight:
        return Icons.stay_current_landscape;
      case SensorOrientation.portraitUpsideDown:
        return Icons.stay_current_portrait;
    }
  }

  void _showDetailedInfo(BuildContext context, OrientationService orientationService) {
    final info = orientationService.getOrientationInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.sensors, color: Colors.blue),
            SizedBox(width: 8),
            Text('Sensor Orientation Info'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Device Orientation', info['deviceOrientation']),
            _buildInfoRow('Metadata Orientation', info['metadataOrientation']),
            _buildInfoRow('Is Landscape', info['isLandscape'].toString()),
            _buildInfoRow('Metadata Position', info['position']),
            const Divider(),
            const Text(
              'Sensor Data:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            _buildInfoRow('Smoothed X', info['smoothedX']),
            _buildInfoRow('Smoothed Y', info['smoothedY']),
            _buildInfoRow('Smoothed Z', info['smoothedZ']),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              orientationService.refreshOrientation();
              Navigator.of(context).pop();
            },
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'monospace',
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }
}

class OrientationDebugPanel extends StatelessWidget {
  const OrientationDebugPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<OrientationService>(
      builder: (context, orientationService, child) {
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.8),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.sensors, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Orientation Sensor Debug',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Current orientation
              _buildDebugRow(
                'Current Orientation',
                orientationService.orientationDescription,
                orientationService.isLandscape ? Colors.green : Colors.blue,
              ),
              
              // Metadata position
              _buildDebugRow(
                'Metadata Position',
                orientationService.getMetadataPosition(),
                orientationService.isLandscape ? Colors.green : Colors.blue,
              ),
              
              // Sensor status
              _buildDebugRow(
                'Sensor Status',
                'Active',
                Colors.green,
              ),
              
              const SizedBox(height: 8),
              
              // Action buttons
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: orientationService.refreshOrientation,
                    icon: const Icon(Icons.refresh, size: 16),
                    label: const Text('Refresh'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _showDetailedSensorInfo(context, orientationService),
                    icon: const Icon(Icons.info, size: 16),
                    label: const Text('Details'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDebugRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: color.withOpacity(0.5)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDetailedSensorInfo(BuildContext context, OrientationService orientationService) {
    final info = orientationService.getOrientationInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detailed Sensor Information'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: info.entries.map((entry) {
              return ListTile(
                title: Text(entry.key),
                subtitle: Text(entry.value.toString()),
                dense: true,
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
