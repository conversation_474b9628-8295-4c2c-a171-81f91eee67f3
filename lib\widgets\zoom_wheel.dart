import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../providers/camera_provider.dart';

class ZoomWheel extends StatefulWidget {
  final bool isVisible;
  
  const ZoomWheel({
    super.key,
    this.isVisible = true,
  });

  @override
  State<ZoomWheel> createState() => _ZoomWheelState();
}

class _ZoomWheelState extends State<ZoomWheel>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isDragging = false;
  double _lastPanAngle = 0.0;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    if (widget.isVisible) {
      _fadeController.forward();
      _scaleController.forward();
    }
  }

  @override
  void didUpdateWidget(ZoomWheel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _fadeController.forward();
        _scaleController.forward();
      } else {
        _fadeController.reverse();
        _scaleController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CameraProvider>(
      builder: (context, cameraProvider, child) {
        if (!cameraProvider.isInitialized) {
          return const SizedBox.shrink();
        }

        return AnimatedBuilder(
          animation: Listenable.merge([_fadeAnimation, _scaleAnimation]),
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.black.withOpacity(0.8),
                        Colors.black.withOpacity(0.6),
                        Colors.black.withOpacity(0.4),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onPanStart: _onPanStart,
                    onPanUpdate: (details) => _onPanUpdate(details, cameraProvider),
                    onPanEnd: _onPanEnd,
                    onTap: () => _resetZoom(cameraProvider),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Zoom wheel background
                        _buildZoomWheel(cameraProvider),
                        
                        // Center zoom indicator
                        _buildCenterIndicator(cameraProvider),
                        
                        // Zoom level text
                        _buildZoomText(cameraProvider),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildZoomWheel(CameraProvider cameraProvider) {
    final zoomProgress = (cameraProvider.zoomLevel - cameraProvider.minZoom) /
        (cameraProvider.maxZoom - cameraProvider.minZoom);
    
    return CustomPaint(
      size: const Size(120, 120),
      painter: ZoomWheelPainter(
        progress: zoomProgress,
        isDragging: _isDragging,
      ),
    );
  }

  Widget _buildCenterIndicator(CameraProvider cameraProvider) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.grey.shade300,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: Colors.white,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Icon(
        Icons.zoom_in,
        color: Colors.black87,
        size: 20,
      ),
    );
  }

  Widget _buildZoomText(CameraProvider cameraProvider) {
    return Positioned(
      bottom: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          '${cameraProvider.zoomLevel.toStringAsFixed(1)}x',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
    });
    _scaleController.forward();
    
    final center = const Offset(60, 60); // Half of 120x120
    final offset = details.localPosition - center;
    _lastPanAngle = math.atan2(offset.dy, offset.dx);
  }

  void _onPanUpdate(DragUpdateDetails details, CameraProvider cameraProvider) {
    final center = const Offset(60, 60);
    final offset = details.localPosition - center;
    final currentAngle = math.atan2(offset.dy, offset.dx);
    
    // Calculate angle difference
    double angleDiff = currentAngle - _lastPanAngle;
    
    // Handle angle wrap-around
    if (angleDiff > math.pi) {
      angleDiff -= 2 * math.pi;
    } else if (angleDiff < -math.pi) {
      angleDiff += 2 * math.pi;
    }
    
    // Convert angle to zoom change
    final sensitivity = 0.5; // Adjust sensitivity
    final zoomRange = cameraProvider.maxZoom - cameraProvider.minZoom;
    final zoomChange = (angleDiff / (2 * math.pi)) * zoomRange * sensitivity;
    
    // Apply zoom change
    final newZoom = (cameraProvider.zoomLevel + zoomChange)
        .clamp(cameraProvider.minZoom, cameraProvider.maxZoom);
    
    cameraProvider.setZoomLevel(newZoom);
    _lastPanAngle = currentAngle;
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDragging = false;
    });
    _scaleController.reverse();
  }

  void _resetZoom(CameraProvider cameraProvider) {
    cameraProvider.setZoomLevel(cameraProvider.minZoom);
    
    // Add haptic feedback
    // HapticFeedback.lightImpact();
  }
}

class ZoomWheelPainter extends CustomPainter {
  final double progress;
  final bool isDragging;

  ZoomWheelPainter({
    required this.progress,
    required this.isDragging,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;
    
    // Draw background circle
    final backgroundPaint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8;
    
    canvas.drawCircle(center, radius, backgroundPaint);
    
    // Draw progress arc
    final progressPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.blue.withOpacity(0.8),
          Colors.cyan.withOpacity(0.8),
          Colors.green.withOpacity(0.8),
          Colors.yellow.withOpacity(0.8),
          Colors.orange.withOpacity(0.8),
          Colors.red.withOpacity(0.8),
        ],
        stops: const [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = isDragging ? 12 : 8
      ..strokeCap = StrokeCap.round;
    
    const startAngle = -math.pi / 2; // Start from top
    final sweepAngle = 2 * math.pi * progress;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
    
    // Draw tick marks
    _drawTickMarks(canvas, center, radius);
  }

  void _drawTickMarks(Canvas canvas, Offset center, double radius) {
    final tickPaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;
    
    const tickCount = 20;
    for (int i = 0; i < tickCount; i++) {
      final angle = (2 * math.pi * i) / tickCount - math.pi / 2;
      final isMainTick = i % 5 == 0;
      final tickLength = isMainTick ? 8.0 : 4.0;
      
      final startRadius = radius - tickLength;
      final endRadius = radius;
      
      final startX = center.dx + startRadius * math.cos(angle);
      final startY = center.dy + startRadius * math.sin(angle);
      final endX = center.dx + endRadius * math.cos(angle);
      final endY = center.dy + endRadius * math.sin(angle);
      
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        tickPaint,
      );
    }
  }

  @override
  bool shouldRepaint(ZoomWheelPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.isDragging != isDragging;
  }
}
