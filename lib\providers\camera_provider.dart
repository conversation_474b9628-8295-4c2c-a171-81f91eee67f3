import 'package:flutter/material.dart';
import 'package:camera/camera.dart' as camera;
import '../services/permission_service.dart';

enum FlashMode { auto, on, off }

class CameraProvider extends ChangeNotifier {
  camera.CameraController? _controller;
  List<camera.CameraDescription> _cameras = [];
  int _selectedCameraIndex = 0;
  FlashMode _flashMode = FlashMode.auto;
  bool _isInitialized = false;
  bool _isRecording = false;
  double _zoomLevel = 1.0;
  double _minZoom = 1.0;
  double _maxZoom = 100.0; // Set maximum zoom to 100X
  String? _errorMessage;
  
  // Getters
  camera.CameraController? get controller => _controller;
  List<camera.CameraDescription> get cameras => _cameras;
  int get selectedCameraIndex => _selectedCameraIndex;
  FlashMode get flashMode => _flashMode;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  double get zoomLevel => _zoomLevel;
  double get minZoom => _minZoom;
  double get maxZoom => _maxZoom;
  String? get errorMessage => _errorMessage;
  
  bool get hasMultipleCameras => _cameras.length > 1;
  bool get isRearCamera => _selectedCameraIndex == 0;
  bool get isFrontCamera => _selectedCameraIndex == 1;
  
  // Initialize camera
  Future<void> initializeCamera() async {
    try {
      _errorMessage = null;
      
      // Check camera permission
      final cameraPermission = await PermissionService.requestCameraPermission();
      if (!cameraPermission) {
        _errorMessage = 'Camera permission denied';
        notifyListeners();
        return;
      }
      
      // Get available cameras
      _cameras = await camera.availableCameras();
      if (_cameras.isEmpty) {
        _errorMessage = 'No cameras available';
        notifyListeners();
        return;
      }
      
      // Initialize with the first camera (usually rear camera)
      await _initializeCameraController(_selectedCameraIndex);
      
    } catch (e) {
      _errorMessage = 'Failed to initialize camera: $e';
      notifyListeners();
    }
  }
  
  // Initialize camera controller
  Future<void> _initializeCameraController(int cameraIndex) async {
    if (_controller != null) {
      await _controller!.dispose();
    }
    
    _controller = camera.CameraController(
      _cameras[cameraIndex],
      camera.ResolutionPreset.max, // Maximum resolution for highest quality
      enableAudio: false,
      imageFormatGroup: camera.ImageFormatGroup.jpeg, // Ensure JPEG format
    );
    
    try {
      await _controller!.initialize();

      // Verify controller is properly initialized
      if (!_controller!.value.isInitialized) {
        throw Exception('Camera controller failed to initialize');
      }

      // Get zoom capabilities
      _minZoom = await _controller!.getMinZoomLevel();
      final deviceMaxZoom = await _controller!.getMaxZoomLevel();
      _maxZoom = deviceMaxZoom.clamp(1.0, 100.0); // Cap at 100X for UI purposes
      _zoomLevel = _minZoom;

      print('DEBUG: Zoom capabilities - Min: $_minZoom, Max: $_maxZoom, Device Max: $deviceMaxZoom');

      // Set initial flash mode
      await _setFlashMode(_flashMode);

      _isInitialized = true;
      _errorMessage = null;
      notifyListeners();

    } catch (e) {
      _errorMessage = 'Failed to initialize camera controller: $e';
      _isInitialized = false;
      notifyListeners();
    }
  }
  
  // Switch camera
  Future<void> switchCamera() async {
    if (_cameras.length < 2) return;
    
    _selectedCameraIndex = _selectedCameraIndex == 0 ? 1 : 0;
    await _initializeCameraController(_selectedCameraIndex);
  }
  
  // Set flash mode
  Future<void> setFlashMode(FlashMode mode) async {
    _flashMode = mode;
    await _setFlashMode(mode);
    notifyListeners();
  }
  
  Future<void> _setFlashMode(FlashMode mode) async {
    if (_controller == null || !_controller!.value.isInitialized) return;
    
    try {
      switch (mode) {
        case FlashMode.auto:
          await _controller!.setFlashMode(camera.FlashMode.auto);
          break;
        case FlashMode.on:
          await _controller!.setFlashMode(camera.FlashMode.always);
          break;
        case FlashMode.off:
          await _controller!.setFlashMode(camera.FlashMode.off);
          break;
      }
    } catch (e) {
      _errorMessage = 'Failed to set flash mode: $e';
      notifyListeners();
    }
  }
  
  // Set zoom level
  Future<void> setZoomLevel(double zoom) async {
    if (_controller == null || !_controller!.value.isInitialized) return;
    
    _zoomLevel = zoom.clamp(_minZoom, _maxZoom);
    await _controller!.setZoomLevel(_zoomLevel);
    notifyListeners();
  }
  
  // Take picture
  Future<camera.XFile?> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      _errorMessage = 'Camera not initialized';
      notifyListeners();
      return null;
    }
    
    try {
      final camera.XFile picture = await _controller!.takePicture();
      return picture;
    } catch (e) {
      _errorMessage = 'Failed to take picture: $e';
      notifyListeners();
      return null;
    }
  }
  
  // Dispose camera
  @override
  Future<void> dispose() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
    _isInitialized = false;
    super.dispose();
  }
  
  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Pause camera (for app lifecycle)
  Future<void> pauseCamera() async {
    if (_controller != null && _controller!.value.isInitialized) {
      try {
        await _controller!.dispose();
        _controller = null;
        _isInitialized = false;
        notifyListeners();
      } catch (e) {
        _errorMessage = 'Failed to pause camera: $e';
        notifyListeners();
      }
    }
  }

  // Reinitialize camera (for app lifecycle)
  Future<void> reinitializeCamera() async {
    if (!_isInitialized && _cameras.isNotEmpty) {
      await _initializeCameraController(_selectedCameraIndex);
    }
  }
}
