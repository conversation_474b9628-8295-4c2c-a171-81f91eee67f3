import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../providers/camera_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/app_state_provider.dart';
import '../providers/gallery_provider.dart';
import '../services/image_service.dart';
import '../services/location_service.dart';
import '../widgets/photo_thumbnail.dart';

class CameraControls extends StatefulWidget {
  const CameraControls({
    super.key,
  });

  @override
  State<CameraControls> createState() => _CameraControlsState();
}

class _CameraControlsState extends State<CameraControls>
    with TickerProviderStateMixin {
  bool _isCapturing = false;

  late AnimationController _captureAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _captureScaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _captureAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Initialize animations
    _captureScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _captureAnimationController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start pulse animation
    _pulseAnimationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _captureAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  Future<void> _takePicture() async {
    if (_isCapturing) return;

    final cameraProvider = Provider.of<CameraProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);

    setState(() {
      _isCapturing = true;
    });

    try {
      // Check if all required metadata fields are filled
      if (appStateProvider.projectName.isEmpty ||
          appStateProvider.beneficiaryName.isEmpty ||
          appStateProvider.gp.isEmpty ||
          appStateProvider.location.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please fill in all required metadata fields before taking a photo'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Get current location
      final locationService = LocationService();
      final position = await locationService.getCurrentPosition();
      if (position != null) {
        appStateProvider.setCoordinates(position.latitude, position.longitude);
      }

      // Set capture time
      appStateProvider.setCaptureDateTime(DateTime.now());



      // Take picture
      final picture = await cameraProvider.takePicture();
      if (picture != null) {
        // Show immediate feedback for faster response
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('📸 Photo captured! Processing...'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 1),
            ),
          );
        }

        // Process and save image with metadata (async for speed) with error handling
        try {
          final imageService = ImageService();
          final galleryProvider = context.read<GalleryProvider>();

          imageService.processAndSaveImage(
            picture,
            appStateProvider,
            settingsProvider,
            onPhotoSaved: (imageData, filename) {
              try {
                // Update gallery provider with new photo for thumbnail
                galleryProvider.updateLastPhoto(imageData, filename);
                print('CAMERA: Gallery provider updated successfully');
              } catch (e) {
                print('CAMERA: Gallery provider update failed: $e');
                // Don't crash if gallery update fails
              }
            },
          ).then((_) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('✅ Photo saved successfully with metadata!'),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }).catchError((e) {
            print('CAMERA: Image processing error: $e');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ Error saving: $e'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 3),
                ),
              );
            }
          });
        } catch (e) {
          print('CAMERA: Critical error in photo processing: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('❌ Critical error: $e'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save photo: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }



  Widget _buildGlassmorphicButton({
    required IconData icon,
    required VoidCallback? onPressed,
    bool isEnabled = true,
  }) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.2),
            Colors.white.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          child: Center(
            child: Icon(
              icon,
              color: isEnabled ? Colors.white : Colors.grey,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<CameraProvider, SettingsProvider>(
      builder: (context, cameraProvider, settingsProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Animated Gallery thumbnail
              const PhotoThumbnail(),

              // Animated Capture button
                  AnimatedBuilder(
                    animation: _captureScaleAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _captureScaleAnimation.value,
                        child: AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                gradient: _isCapturing
                                    ? const LinearGradient(
                                        colors: [Colors.grey, Colors.grey],
                                      )
                                    : LinearGradient(
                                        colors: [
                                          Colors.white,
                                          Colors.grey.shade100,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 4,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                  if (!_isCapturing)
                                    BoxShadow(
                                      color: Colors.white.withOpacity(0.5 * _pulseAnimation.value),
                                      blurRadius: 20 * _pulseAnimation.value,
                                      spreadRadius: 5 * _pulseAnimation.value,
                                    ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(40),
                                  onTap: _isCapturing ? null : () {
                                    _captureAnimationController.forward().then((_) {
                                      _captureAnimationController.reverse();
                                    });
                                    _takePicture();
                                  },
                                  child: Center(
                                    child: _isCapturing
                                        ? const CircularProgressIndicator(
                                            color: Colors.black,
                                            strokeWidth: 3,
                                          )
                                        : const Icon(
                                            Icons.camera_alt,
                                            color: Colors.black,
                                            size: 32,
                                          ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),

              // Animated Camera switch button
              _buildGlassmorphicButton(
                icon: Icons.flip_camera_ios,
                onPressed: cameraProvider.hasMultipleCameras
                    ? () => cameraProvider.switchCamera()
                    : null,
                isEnabled: cameraProvider.hasMultipleCameras,
              ),
            ],
          ),
        );
      },
    );
  }
}
