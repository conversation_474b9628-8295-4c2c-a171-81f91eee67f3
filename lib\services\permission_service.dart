import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

class PermissionService {
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }

  static Future<bool> requestStoragePermission() async {
    // For Android 13+ (API 33+), we need different permissions
    if (await _isAndroid13OrHigher()) {
      final status = await Permission.photos.request();
      return status == PermissionStatus.granted;
    } else {
      final status = await Permission.storage.request();
      return status == PermissionStatus.granted;
    }
  }

  static Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status == PermissionStatus.granted;
  }

  static Future<bool> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status == PermissionStatus.granted;
  }

  static Future<bool> checkStoragePermission() async {
    if (await _isAndroid13OrHigher()) {
      final status = await Permission.photos.status;
      return status == PermissionStatus.granted;
    } else {
      final status = await Permission.storage.status;
      return status == PermissionStatus.granted;
    }
  }

  static Future<bool> checkLocationPermission() async {
    final status = await Permission.location.status;
    return status == PermissionStatus.granted;
  }

  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};
    
    // Request camera permission
    results['camera'] = await requestCameraPermission();
    
    // Request storage permission
    results['storage'] = await requestStoragePermission();
    
    // Request location permission
    results['location'] = await requestLocationPermission();
    
    return results;
  }

  static Future<Map<String, bool>> checkAllPermissions() async {
    final results = <String, bool>{};
    
    results['camera'] = await checkCameraPermission();
    results['storage'] = await checkStoragePermission();
    results['location'] = await checkLocationPermission();
    
    return results;
  }

  static Future<bool> _isAndroid13OrHigher() async {
    // This is a simplified check. In a real app, you might want to use
    // device_info_plus package to get the actual Android version
    return true; // Assume modern Android for now
  }

  static void showPermissionDialog(
    BuildContext context,
    String permissionName,
    String reason,
    VoidCallback onRetry,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('$permissionName Permission Required'),
          content: Text(reason),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Settings'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  static void showPermissionRationale(
    BuildContext context,
    String title,
    String message,
    VoidCallback onAccept,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onAccept();
              },
              child: const Text('Allow'),
            ),
          ],
        );
      },
    );
  }

  static String getCameraPermissionRationale() {
    return 'This app needs camera access to take photos. '
           'Camera permission is essential for the core functionality of this app.';
  }

  static String getStoragePermissionRationale() {
    return 'This app needs storage access to save photos to your gallery. '
           'Without this permission, photos cannot be saved.';
  }

  static String getLocationPermissionRationale() {
    return 'This app needs location access to add GPS coordinates to your photos. '
           'This helps with organizing and geotagging your images.';
  }
}
