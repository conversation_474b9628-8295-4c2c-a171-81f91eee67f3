import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum MetadataPosition { topLeft, topRight, bottomLeft, bottomRight }

class SettingsProvider extends ChangeNotifier {
  // Font settings
  double _fontSize = 14.0;
  Color _fontColor = Colors.white;

  // Metadata settings
  MetadataPosition _metadataPosition = MetadataPosition.bottomLeft;
  
  // Getters
  double get fontSize => _fontSize;
  Color get fontColor => _fontColor;
  MetadataPosition get metadataPosition => _metadataPosition;


  
  // Setters
  void setFontSize(double size) {
    _fontSize = size;
    _saveSettings();
    notifyListeners();
  }

  void setFontColor(Color color) {
    _fontColor = color;
    _saveSettings();
    notifyListeners();
  }

  void setMetadataPosition(MetadataPosition position) {
    _metadataPosition = position;
    _saveSettings();
    notifyListeners();
  }
  
  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    _fontSize = prefs.getDouble('fontSize') ?? 14.0;
    _fontColor = Color(prefs.getInt('fontColor') ?? Colors.white.value);
    _metadataPosition = MetadataPosition.values[prefs.getInt('metadataPosition') ?? 0];

    notifyListeners();
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setDouble('fontSize', _fontSize);
    await prefs.setInt('fontColor', _fontColor.value);
    await prefs.setInt('metadataPosition', _metadataPosition.index);
  }
}
