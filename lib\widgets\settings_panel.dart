import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class SettingsPanel extends StatelessWidget {
  const SettingsPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 400),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, (1 - value) * 300),
              child: Container(
                height: MediaQuery.of(context).size.height * 0.8,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFa8edea),
                      Color(0xFFfed6e3),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Enhanced Title
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.settings,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Camera Settings',
                      style: TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2c3e50),
                        letterSpacing: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Settings content
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  children: [
                    // Font settings
                    _buildSectionTitle('Live Preview Font Settings'),
                    _buildFontSizeSlider(settingsProvider),
                    const SizedBox(height: 16),
                    _buildFontColorPicker(settingsProvider),
                    const SizedBox(height: 24),

                    // Meta_Data font settings
                    _buildSectionTitle('Meta_Data Font Size (Saved Photos)'),
                    _buildMetadataFontSizeSlider(settingsProvider),
                    const SizedBox(height: 24),

                    // Metadata position
                    _buildSectionTitle('Metadata Position'),
                    _buildMetadataPositionSelector(settingsProvider),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }



  Widget _buildFontSizeSlider(SettingsProvider settingsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Live Preview Font Size: ${settingsProvider.fontSize.toInt()}'),
        Slider(
          value: settingsProvider.fontSize,
          min: 10,
          max: 24,
          divisions: 14,
          onChanged: (value) => settingsProvider.setFontSize(value),
        ),
      ],
    );
  }

  Widget _buildMetadataFontSizeSlider(SettingsProvider settingsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Meta_Data Font Size: ${settingsProvider.metadataFontSize.toInt()}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Controls font size in saved photos only',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Slider(
          value: settingsProvider.metadataFontSize,
          min: 12,
          max: 48, // Increased maximum for larger Meta_Data fonts
          divisions: 36, // More divisions for finer control
          activeColor: Colors.blue,
          inactiveColor: Colors.blue[100],
          onChanged: (value) => settingsProvider.setMetadataFontSize(value),
        ),
        Text(
          'Range: 12-36 (Larger sizes for better visibility)',
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  Widget _buildFontColorPicker(SettingsProvider settingsProvider) {
    final colors = [
      Colors.white,
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.yellow,
      Colors.orange,
      Colors.purple,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Font Color'),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: colors.map((color) {
            return GestureDetector(
              onTap: () => settingsProvider.setFontColor(color),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: settingsProvider.fontColor == color
                        ? Colors.black
                        : Colors.grey[300]!,
                    width: settingsProvider.fontColor == color ? 3 : 1,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildMetadataPositionSelector(SettingsProvider settingsProvider) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      children: MetadataPosition.values.map((position) {
        return GestureDetector(
          onTap: () => settingsProvider.setMetadataPosition(position),
          child: Container(
            decoration: BoxDecoration(
              color: settingsProvider.metadataPosition == position
                  ? Colors.blue[100]
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: settingsProvider.metadataPosition == position
                    ? Colors.blue
                    : Colors.grey[300]!,
              ),
            ),
            child: Center(
              child: Text(
                _getPositionName(position),
                style: TextStyle(
                  color: settingsProvider.metadataPosition == position
                      ? Colors.blue[800]
                      : Colors.black87,
                  fontWeight: settingsProvider.metadataPosition == position
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }





  String _getPositionName(MetadataPosition position) {
    switch (position) {
      case MetadataPosition.topLeft:
        return 'Top Left';
      case MetadataPosition.topRight:
        return 'Top Right';
      case MetadataPosition.bottomLeft:
        return 'Bottom Left';
      case MetadataPosition.bottomRight:
        return 'Bottom Right';
    }
  }


}
