import 'package:flutter/material.dart';

class GridlinesOverlay extends StatelessWidget {
  const GridlinesOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON>rid<PERSON><PERSON><PERSON><PERSON>(),
      child: Container(),
    );
  }
}

class GridlinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw rule of thirds grid
    // Vertical lines
    final verticalLine1 = size.width / 3;
    final verticalLine2 = (size.width / 3) * 2;
    
    canvas.drawLine(
      Offset(verticalLine1, 0),
      Offset(verticalLine1, size.height),
      paint,
    );
    
    canvas.drawLine(
      Offset(verticalLine2, 0),
      Offset(verticalLine2, size.height),
      paint,
    );

    // Horizontal lines
    final horizontalLine1 = size.height / 3;
    final horizontalLine2 = (size.height / 3) * 2;
    
    canvas.drawLine(
      Offset(0, horizontalLine1),
      Offset(size.width, horizontalLine1),
      paint,
    );
    
    canvas.drawLine(
      Offset(0, horizontalLine2),
      Offset(size.width, horizontalLine2),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
