import 'package:flutter/material.dart';
import 'package:flutter/services.dart' as flutter_services;
import 'package:provider/provider.dart';
import 'providers/app_state_provider.dart';
import 'providers/camera_provider.dart';
import 'providers/settings_provider.dart';
import 'services/orientation_service.dart';
import 'screens/camera_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await flutter_services.SystemChrome.setPreferredOrientations([
    flutter_services.DeviceOrientation.portraitUp,
    flutter_services.DeviceOrientation.portraitDown,
    flutter_services.DeviceOrientation.landscapeLeft,
    flutter_services.DeviceOrientation.landscapeRight,
  ]);

  // Initialize orientation service
  final orientationService = OrientationService();
  orientationService.initialize();

  runApp(PCamApp(orientationService: orientationService));
}

class PCamApp extends StatelessWidget {
  final OrientationService orientationService;

  const PCamApp({
    super.key,
    required this.orientationService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppStateProvider()),
        ChangeNotifierProvider(create: (_) => CameraProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ChangeNotifierProvider.value(value: orientationService),
      ],
      child: MaterialApp(
        title: 'PCam - Professional Camera',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const CameraScreen(),
      ),
    );
  }
}


