import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/app_state_provider.dart';
import '../providers/settings_provider.dart';
import '../services/orientation_service.dart';

class MetadataOverlay extends StatelessWidget {
  const MetadataOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer3<AppStateProvider, SettingsProvider, OrientationService>(
      builder: (context, appStateProvider, settingsProvider, orientationService, child) {
        final metadata = _buildMetadataText(appStateProvider);

        if (metadata.isEmpty) {
          return const SizedBox.shrink();
        }

        // Use sensor-based orientation detection
        final isLandscape = orientationService.isLandscape;

        // Override position based on sensor orientation
        final orientationPosition = isLandscape
            ? MetadataPosition.topLeft
            : MetadataPosition.bottomLeft;

        return Positioned.fill(
          child: _buildPositionedMetadata(
            metadata,
            orientationPosition,
            settingsProvider.fontSize,
            settingsProvider.fontColor,
            isLandscape,
          ),
        );
      },
    );
  }

  String _buildMetadataText(AppStateProvider appStateProvider) {
    final List<String> metadataLines = [];

    if (appStateProvider.projectName.isNotEmpty) {
      metadataLines.add('🏗 Project: ${appStateProvider.projectName}');
    }

    if (appStateProvider.beneficiaryName.isNotEmpty) {
      metadataLines.add('🧑‍💼 Beneficiary: ${appStateProvider.beneficiaryName}');
    }

    if (appStateProvider.gp.isNotEmpty) {
      metadataLines.add('🗺 G.P.: ${appStateProvider.gp}');
    }

    if (appStateProvider.location.isNotEmpty) {
      metadataLines.add('📍 Location: ${appStateProvider.location}');
    }

    if (appStateProvider.note.isNotEmpty) {
      metadataLines.add('📝 Note: ${appStateProvider.note}');
    }

    if (appStateProvider.latitude != null && appStateProvider.longitude != null) {
      metadataLines.add(
        '📍 GPS: ${appStateProvider.latitude!.toStringAsFixed(6)}, ${appStateProvider.longitude!.toStringAsFixed(6)}',
      );
    }

    if (appStateProvider.captureDateTime != null) {
      final formatter = DateFormat('📆 dd/MM/yyyy HH:mm:ss');
      metadataLines.add(formatter.format(appStateProvider.captureDateTime!));
    } else {
      // Show current time as preview
      final formatter = DateFormat('📆 dd/MM/yyyy HH:mm:ss');
      metadataLines.add(formatter.format(DateTime.now()));
    }

    return metadataLines.join('\n');
  }

  Widget _buildPositionedMetadata(
    String metadata,
    MetadataPosition position,
    double fontSize,
    Color fontColor,
    bool isLandscape,
  ) {
    final textWidget = Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isLandscape && position == MetadataPosition.topLeft
          ? Transform.rotate(
              angle: 1.5708, // +90 degrees in radians (90° right/clockwise rotation)
              child: Text(
                metadata,
                style: TextStyle(
                  color: fontColor,
                  fontSize: fontSize + 2,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 3,
                      color: Colors.black.withOpacity(0.9),
                    ),
                    Shadow(
                      offset: const Offset(-1, -1),
                      blurRadius: 3,
                      color: Colors.black.withOpacity(0.9),
                    ),
                  ],
                ),
              ),
            )
          : Text(
              metadata,
              style: TextStyle(
                color: fontColor,
                fontSize: fontSize + 2,
                fontWeight: FontWeight.bold,
                letterSpacing: 0.5,
                shadows: [
                  Shadow(
                    offset: const Offset(1, 1),
                    blurRadius: 3,
                    color: Colors.black.withOpacity(0.9),
                  ),
                  Shadow(
                    offset: const Offset(-1, -1),
                    blurRadius: 3,
                    color: Colors.black.withOpacity(0.9),
                  ),
                ],
              ),
            ),
    );

    // Adjust positioning based on orientation with improved padding
    final padding = isLandscape ? 40.0 : 30.0;

    switch (position) {
      case MetadataPosition.topLeft:
        return Positioned(
          top: padding,
          left: padding,
          child: textWidget,
        );
      case MetadataPosition.topRight:
        return Positioned(
          top: padding,
          right: padding,
          child: textWidget,
        );
      case MetadataPosition.bottomLeft:
        return Positioned(
          bottom: padding,
          left: padding,
          child: textWidget,
        );
      case MetadataPosition.bottomRight:
        return Positioned(
          bottom: padding,
          right: padding,
          child: textWidget,
        );
    }
  }
}
