import 'package:flutter/material.dart';

class AppStateProvider extends ChangeNotifier {
  // Metadata fields
  String _projectName = '';
  String _beneficiaryName = '';
  String _gp = '';
  String _location = '';
  String _note = '';
  double? _latitude;
  double? _longitude;
  DateTime? _captureDateTime;
  
  // UI state
  bool _isLoading = false;
  String? _errorMessage;
  
  // Getters
  String get projectName => _projectName;
  String get beneficiaryName => _beneficiaryName;
  String get gp => _gp;
  String get location => _location;
  String get note => _note;
  double? get latitude => _latitude;
  double? get longitude => _longitude;
  DateTime? get captureDateTime => _captureDateTime;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  
  // Setters
  void setProjectName(String name) {
    _projectName = name;
    notifyListeners();
  }
  
  void setBeneficiaryName(String name) {
    _beneficiaryName = name;
    notifyListeners();
  }

  void setGP(String gp) {
    _gp = gp;
    notifyListeners();
  }

  void setLocation(String location) {
    _location = location;
    notifyListeners();
  }

  void setNote(String note) {
    _note = note;
    notifyListeners();
  }
  
  void setCoordinates(double latitude, double longitude) {
    _latitude = latitude;
    _longitude = longitude;
    notifyListeners();
  }
  
  void setCaptureDateTime(DateTime dateTime) {
    _captureDateTime = dateTime;
    notifyListeners();
  }
  
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // Reset all metadata
  void resetMetadata() {
    _projectName = '';
    _beneficiaryName = '';
    _location = '';
    _latitude = null;
    _longitude = null;
    _captureDateTime = null;
    notifyListeners();
  }
}
