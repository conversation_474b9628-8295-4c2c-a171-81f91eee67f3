import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

class GalleryProvider extends ChangeNotifier {
  // Last saved photo data for thumbnail
  Uint8List? _lastPhotoThumbnail;
  String? _lastPhotoPath;
  DateTime? _lastPhotoTime;
  List<GalleryPhoto> _recentPhotos = [];
  bool _isLoading = false;

  // Getters
  Uint8List? get lastPhotoThumbnail => _lastPhotoThumbnail;
  String? get lastPhotoPath => _lastPhotoPath;
  DateTime? get lastPhotoTime => _lastPhotoTime;
  List<GalleryPhoto> get recentPhotos => _recentPhotos;
  bool get isLoading => _isLoading;
  bool get hasLastPhoto => _lastPhotoThumbnail != null;

  // Update last photo after saving
  void updateLastPhoto(Uint8List imageData, String filename) {
    _lastPhotoThumbnail = imageData;
    _lastPhotoPath = filename;
    _lastPhotoTime = DateTime.now();
    
    // Add to recent photos list
    final photo = GalleryPhoto(
      imageData: imageData,
      filename: filename,
      timestamp: DateTime.now(),
    );
    
    _recentPhotos.insert(0, photo); // Add to beginning
    
    // Keep only last 20 photos for performance
    if (_recentPhotos.length > 20) {
      _recentPhotos = _recentPhotos.take(20).toList();
    }
    
    notifyListeners();
    print('GALLERY: Updated last photo thumbnail');
  }

  // Load recent photos from gallery
  Future<void> loadRecentPhotos() async {
    try {
      _isLoading = true;
      notifyListeners();

      // Get recent photos from PCam album
      // Note: This is a simplified implementation
      // In a real app, you'd use Gal.getPhotos() or similar
      
      print('GALLERY: Loading recent photos...');
      
      // For now, we'll just keep the photos we've saved in this session
      // In a full implementation, you'd load from the actual gallery
      
      _isLoading = false;
      notifyListeners();
      
    } catch (e) {
      print('GALLERY: Error loading photos: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  // Share photo
  Future<void> sharePhoto(GalleryPhoto photo) async {
    try {
      print('GALLERY: Sharing photo ${photo.filename}');
      
      // Create temp file for sharing
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/${photo.filename}');
      await tempFile.writeAsBytes(photo.imageData);
      
      // Share the file
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: 'Photo taken with PCam - Professional Camera App',
      );
      
      // Clean up temp file after a delay
      Future.delayed(const Duration(seconds: 5), () {
        tempFile.delete().catchError((e) => print('Failed to delete temp file: $e'));
      });
      
    } catch (e) {
      print('GALLERY: Error sharing photo: $e');
      throw Exception('Failed to share photo: $e');
    }
  }

  // Delete photo
  Future<void> deletePhoto(GalleryPhoto photo) async {
    try {
      print('GALLERY: Deleting photo ${photo.filename}');
      
      // Remove from recent photos list
      _recentPhotos.removeWhere((p) => p.filename == photo.filename);
      
      // If this was the last photo, clear it
      if (_lastPhotoPath == photo.filename) {
        _lastPhotoThumbnail = null;
        _lastPhotoPath = null;
        _lastPhotoTime = null;
      }
      
      notifyListeners();
      
      // Note: Actual deletion from device gallery would require
      // platform-specific implementation or gallery management APIs
      
    } catch (e) {
      print('GALLERY: Error deleting photo: $e');
      throw Exception('Failed to delete photo: $e');
    }
  }

  // Clear all photos
  void clearPhotos() {
    _lastPhotoThumbnail = null;
    _lastPhotoPath = null;
    _lastPhotoTime = null;
    _recentPhotos.clear();
    notifyListeners();
  }
}

class GalleryPhoto {
  final Uint8List imageData;
  final String filename;
  final DateTime timestamp;

  GalleryPhoto({
    required this.imageData,
    required this.filename,
    required this.timestamp,
  });
}
